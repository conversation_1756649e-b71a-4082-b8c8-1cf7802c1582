# Convex deployment URL (auto-generated)
CONVEX_DEPLOYMENT=

# OpenRouter API key for AI models
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Firecrawl API key for web scraping
FIRECRAWL_API_KEY=your_firecrawl_api_key_here

# Exa API key for web search
EXA_API_KEY=your_exa_api_key_here

# xAI API key for Live Search
XAI_API_KEY=your_xai_api_key_here

# Twitter API key (docs.twitterapi.io)
TWITTER_API_KEY=your_twitterapi_io_key_here
# Webhook secret for verifying inbound Twitter webhook
TWITTER_WEBHOOK_SECRET=


# Admin authentication (optional - for multi-user setup)
ADMIN_USER_ID=default_admin

# Rate limiting configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_DAY=1000

# Cache TTL settings (in milliseconds)

# Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=

EXA_CACHE_TTL=3600000
FIRECRAWL_CACHE_TTL=7200000
TWITTER_CACHE_TTL=1800000