{"name": "Ask<PERSON><PERSON><PERSON>", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @AskBuddy/backend dev", "dev:setup": "turbo -F @AskBuddy/backend dev:setup", "test": "bun test"}, "dependencies": {}, "devDependencies": {"convex": "^1.25.4", "turbo": "^2.5.4"}, "packageManager": "bun@1.2.19"}