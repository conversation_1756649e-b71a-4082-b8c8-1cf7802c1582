import { describe, test, expect } from "bun:test";

// Simple rate decision helper (simulate current implementation)
function canPost(minuteCount: number, minuteLimit: number, dayCount: number, dayLimit: number) {
  return minuteCount < minuteLimit && dayCount < dayLimit;
}

describe("rate decisions", () => {
  test("within limits", () => {
    expect(canPost(2, 5, 10, 100)).toBe(true);
  });
  test("hit minute limit", () => {
    expect(canPost(5, 5, 0, 100)).toBe(false);
  });
  test("hit day limit", () => {
    expect(canPost(0, 5, 100, 100)).toBe(false);
  });
});

