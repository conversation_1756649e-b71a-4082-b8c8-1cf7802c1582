import { normalizeTwitterPayload, verifyWebhookSecret } from "../packages/backend/convex/util/webhook";

function mkHeaders(secret?: string) {
  const h = new Headers();
  if (secret) h.set("x-webhook-secret", secret);
  return h;
}

describe("webhook utils", () => {
  test("verifyWebhookSecret - ok", () => {
    // @ts-ignore
    process.env.TWITTER_WEBHOOK_SECRET = "abc";
    expect(verifyWebhookSecret(mkHeaders("abc"))).toBe(true);
  });
  test("verifyWebhookSecret - fail", () => {
    // @ts-ignore
    process.env.TWITTER_WEBHOOK_SECRET = "abc";
    expect(verifyWebhookSecret(mkHeaders("xyz"))).toBe(false);
  });

  test("normalizeTwitterPayload - minimal", () => {
    const body = { data: { id: "123", user: { id: "u1", screen_name: "askbuddy" }, text: "hi @AskBuddy" } };
    const n = normalizeTwitterPayload(body)!;
    expect(n.tweetId).toBe("123");
    expect(n.authorUserId).toBe("u1");
    expect(n.text).toContain("hi");
  });

  test("normalizeTwitterPayload - invalid", () => {
    const n = normalizeTwitterPayload({});
    expect(n).toBeNull();
  });
});

