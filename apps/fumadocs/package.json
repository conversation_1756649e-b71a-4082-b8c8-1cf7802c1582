{"name": "fumadocs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo --port=4000", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"next": "15.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "fumadocs-ui": "15.6.10", "fumadocs-core": "15.6.10", "fumadocs-mdx": "11.7.5"}, "devDependencies": {"@types/node": "24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "typescript": "^5.9.2", "@types/mdx": "^2.0.13", "@tailwindcss/postcss": "^4.1.11", "tailwindcss": "^4.1.11", "postcss": "^8.5.6"}}