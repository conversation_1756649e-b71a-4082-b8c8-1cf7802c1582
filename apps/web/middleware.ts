import { clerkMiddleware } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

export default clerkMiddleware(async (auth, req) => {
  const { pathname } = req.nextUrl;
  if (pathname.startsWith("/admin")) {
    const { userId, redirectToSignIn } = await auth();
    if (!userId) return redirectToSignIn();
  }
  return NextResponse.next();
});

export const config = {
  matcher: [
    "/((?!_next|.*\.(?:html|css|js|json|svg|png|jpg|jpeg|gif|ico|txt|xml|map)).*)",
  ],
};

