{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@AskBuddy/backend": "workspace:*", "@ai-sdk/ui-utils": "^1.1.0", "@clerk/nextjs": "^6.30.1", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.69.0", "ai": "^4.0.31", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.4", "date-fns": "^4.1.0", "lucide-react": "^0.487.0", "next": "15.3.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.1.10", "typescript": "^5"}}