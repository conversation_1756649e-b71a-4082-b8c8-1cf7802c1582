"use client";

import { useQuery } from "convex/react";
import { api } from "@AskBuddy/backend";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  MessageSquare,
  Bot,
  Zap,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  BarChart3,
  Settings,
  Plus,
} from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";

export default function AdminDashboard() {
  // Get all workspaces - in production this would be filtered by user
  const workspaces = useQuery(api.admin.getWorkspaces);
  const workspace = workspaces?.[0]; // Get first workspace for now

  // Get data for the workspace
  const botAccounts = useQuery(
    api.admin.getBotAccounts,
    workspace ? { workspaceId: workspace._id } : "skip"
  );
  
  const agentProfiles = useQuery(
    api.admin.getAgentProfiles,
    workspace ? { workspaceId: workspace._id } : "skip"
  );

  const dailyMetrics = useQuery(
    api.admin.getDailyMetrics,
    workspace ? { 
      workspaceId: workspace._id,
      dateRange: {
        start: format(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
        end: format(new Date(), 'yyyy-MM-dd')
      }
    } : "skip"
  );

  // Show setup message if no workspace exists
  if (workspaces && workspaces.length === 0) {
    return (
      <div className="p-8 max-w-2xl mx-auto">
        <Card>
          <CardContent className="p-8 text-center">
            <Bot className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">Welcome to AskBuddy!</h2>
            <p className="text-gray-600 mb-6">
              Let's set up your Twitter bot to start responding to mentions automatically.
            </p>
            <Link href="/admin/setup">
              <Button size="lg">
                <Plus className="w-5 h-5 mr-2" />
                Start Setup
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Loading state
  if (!workspaces || !workspace) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Calculate metrics from real data
  const totalBots = botAccounts?.length || 0;
  const activeBots = botAccounts?.filter(bot => bot.auto_reply_enabled)?.length || 0;
  
  const totalMentions = dailyMetrics?.reduce((sum, day) => sum + (day.mentions || 0), 0) || 0;
  const totalReplies = dailyMetrics?.reduce((sum, day) => sum + (day.replies_posted || 0), 0) || 0;
  
  const successRate = totalMentions > 0 ? ((totalReplies / totalMentions) * 100).toFixed(1) : "0";
  const avgLatency = dailyMetrics?.reduce((sum, day) => sum + (day.avg_latency_ms || 0), 0) / (dailyMetrics?.length || 1) || 0;

  const metrics = [
    {
      name: "Total Mentions",
      value: totalMentions.toString(),
      change: "+12%", // TODO: Calculate from previous period
      changeType: "positive" as const,
      icon: MessageSquare,
    },
    {
      name: "Active Bots",
      value: `${activeBots}/${totalBots}`,
      change: totalBots > 0 ? "+1" : "0",
      changeType: "positive" as const,
      icon: Bot,
    },
    {
      name: "Replies Posted",
      value: totalReplies.toString(),
      change: "+18%", // TODO: Calculate from previous period
      changeType: "positive" as const,
      icon: Zap,
    },
    {
      name: "Success Rate",
      value: `${successRate}%`,
      change: "+2.1%", // TODO: Calculate from previous period
      changeType: "positive" as const,
      icon: TrendingUp,
    },
  ];

  return (
    <div className="p-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {workspace.name} Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Monitor your Twitter bot's performance and activity
            </p>
          </div>
          <div className="flex gap-3">
            <Link href="/admin/setup">
              <Button variant="outline">
                <Plus className="w-4 h-4 mr-2" />
                Add Bot
              </Button>
            </Link>
            <Link href="/admin/bot">
              <Button>
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric) => (
          <Card key={metric.name}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <metric.icon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {metric.name}
                  </p>
                  <div className="flex items-center">
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {metric.value}
                    </p>
                    <span
                      className={`ml-2 text-sm ${
                        metric.changeType === "positive"
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {metric.change}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Bot Status & Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Bot Accounts</CardTitle>
          </CardHeader>
          <CardContent>
            {botAccounts && botAccounts.length > 0 ? (
              <div className="space-y-4">
                {botAccounts.map((bot) => (
                  <div
                    key={bot._id}
                    className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="flex items-center">
                      <Bot className="h-8 w-8 text-blue-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          @{bot.username}
                        </p>
                        <p className="text-sm text-gray-500">
                          {bot.display_name || `Bot ID: ${bot.x_user_id}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          bot.auto_reply_enabled ? "bg-green-500" : "bg-gray-400"
                        }`}
                      />
                      <span
                        className={`text-sm font-medium ${
                          bot.auto_reply_enabled ? "text-green-600" : "text-gray-500"
                        }`}
                      >
                        {bot.auto_reply_enabled ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No bot accounts configured</p>
                <Link href="/admin/setup">
                  <Button className="mt-4">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Your First Bot
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Agent Profiles</CardTitle>
          </CardHeader>
          <CardContent>
            {agentProfiles && agentProfiles.length > 0 ? (
              <div className="space-y-4">
                {agentProfiles.map((profile) => (
                  <div
                    key={profile._id}
                    className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="flex items-center">
                      <Zap className="h-8 w-8 text-purple-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {profile.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {profile.model_primary}
                        </p>
                      </div>
                    </div>
                    <Link href="/admin/ai">
                      <Button variant="ghost" size="sm">
                        <Settings className="w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No AI agent profiles configured</p>
                <Link href="/admin/setup">
                  <Button className="mt-4" variant="outline">
                    <Plus className="w-4 h-4 mr-2" />
                    Configure AI Agent
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Daily Metrics Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Activity (Last 7 Days)</CardTitle>
        </CardHeader>
        <CardContent>
          {dailyMetrics && dailyMetrics.length > 0 ? (
            <div className="space-y-4">
              {dailyMetrics.map((day) => (
                <div
                  key={day.date_utc}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="font-medium">
                    {format(new Date(day.date_utc), 'MMM dd, yyyy')}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{day.mentions} mentions</span>
                    <span>{day.replies_posted} replies</span>
                    <span>{day.avg_latency_ms}ms avg</span>
                    <span>${day.costs_usd_est.toFixed(3)} cost</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 dark:text-gray-400">
                  No activity data available yet
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Data will appear once your bot starts processing mentions
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}