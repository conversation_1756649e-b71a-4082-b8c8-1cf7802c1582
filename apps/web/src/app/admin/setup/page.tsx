"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@AskBuddy/backend";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Bot, 
  Key, 
  Zap,
  ArrowRight,
  Loader2
} from "lucide-react";
import { toast } from "sonner";

type SetupStep = "workspace" | "bot" | "agent" | "keys" | "complete";

export default function SetupPage() {
  const [currentStep, setCurrentStep] = useState<SetupStep>("workspace");
  const [loading, setLoading] = useState(false);
  const [workspaceData, setWorkspaceData] = useState({
    name: "",
    timezone: "UTC",
  });
  const [botData, setBotData] = useState({
    username: "",
    xUserId: "",
    displayName: "",
    twitterApiKey: "",
    autoReplyEnabled: true,
    perMinuteLimit: 5,
    perDayLimit: 100,
  });
  const [agentData, setAgentData] = useState({
    systemPrompt: `You are AskBuddy, a helpful Twitter bot that provides informative and accurate responses to mentions.

Your capabilities include:
- Searching the web for current information using Exa
- Scraping and analyzing web pages using Firecrawl
- Searching Twitter/X content using xAI Live Search
- Providing cited, well-researched answers

Guidelines:
- Always be helpful and informative
- Cite your sources when using external information
- Keep responses concise but comprehensive (under 280 characters when possible for Twitter)
- Be respectful and avoid controversial topics
- If you cannot find relevant information, say so honestly`,
    temperature: 0.7,
    modelPrimary: "openai/gpt-4o-mini",
    enableExa: true,
    enableFirecrawl: true,
    enableXAI: true,
  });
  const [keysData, setKeysData] = useState({
    openrouter: "",
    exa: "",
    firecrawl: "",
    xai: "",
  });

  const createWorkspace = useMutation(api.admin.createWorkspace);
  const createBotAccount = useMutation(api.admin.createBotAccount);
  const createAgentProfile = useMutation(api.admin.createDefaultAgentProfile);
  const updateAgentProfile = useMutation(api.admin.updateAgentProfile);
  const addProviderKey = useMutation(api.admin.addProviderKey);

  const steps = [
    { id: "workspace", title: "Workspace", icon: Settings, completed: false },
    { id: "bot", title: "Bot Account", icon: Bot, completed: false },
    { id: "agent", title: "AI Agent", icon: Zap, completed: false },
    { id: "keys", title: "API Keys", icon: Key, completed: false },
    { id: "complete", title: "Complete", icon: CheckCircle, completed: false },
  ];

  const handleWorkspaceSubmit = async () => {
    if (!workspaceData.name.trim()) {
      toast.error("Please enter a workspace name");
      return;
    }

    setLoading(true);
    try {
      const workspaceId = await createWorkspace({
        name: workspaceData.name,
        ownerUserId: "default_admin", // TODO: Use actual auth user ID
        timezone: workspaceData.timezone,
      });
      
      // Store workspace ID for next steps
      sessionStorage.setItem("setupWorkspaceId", workspaceId);
      toast.success("Workspace created successfully!");
      setCurrentStep("bot");
    } catch (error) {
      toast.error("Failed to create workspace");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleBotSubmit = async () => {
    if (!botData.username.trim() || !botData.xUserId.trim()) {
      toast.error("Please enter both username and X user ID");
      return;
    }

    const workspaceId = sessionStorage.getItem("setupWorkspaceId");
    if (!workspaceId) {
      toast.error("Workspace not found. Please start over.");
      return;
    }

    setLoading(true);
    try {
      const botAccountId = await createBotAccount({
        workspaceId: workspaceId as any,
        xUserId: botData.xUserId,
        username: botData.username,
        displayName: botData.displayName,
        provider: "docs.twitterapi.io",
        keyLabel: "Twitter API",
        keyLast4: botData.twitterApiKey.slice(-4),
      });

      sessionStorage.setItem("setupBotAccountId", botAccountId);
      toast.success("Bot account configured!");
      setCurrentStep("agent");
    } catch (error) {
      toast.error("Failed to configure bot account");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleAgentSubmit = async () => {
    const workspaceId = sessionStorage.getItem("setupWorkspaceId");
    if (!workspaceId) {
      toast.error("Workspace not found. Please start over.");
      return;
    }

    setLoading(true);
    try {
      const profileId = await createAgentProfile({
        workspaceId: workspaceId as any,
      });

      await updateAgentProfile({
        profileId,
        systemPrompt: agentData.systemPrompt,
        temperature: agentData.temperature,
        modelPrimary: agentData.modelPrimary,
        toolDefaults: {
          firecrawl_enabled: agentData.enableFirecrawl,
          exa_enabled: agentData.enableExa,
          xai_live_search_enabled: agentData.enableXAI,
          xai_search_max_results: 5,
          safe_search_web_news: true,
        },
      });

      sessionStorage.setItem("setupAgentProfileId", profileId);
      toast.success("AI agent configured!");
      setCurrentStep("keys");
    } catch (error) {
      toast.error("Failed to configure AI agent");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeysSubmit = async () => {
    const workspaceId = sessionStorage.getItem("setupWorkspaceId");
    if (!workspaceId) {
      toast.error("Workspace not found. Please start over.");
      return;
    }

    const keys = Object.entries(keysData).filter(([_, value]) => value.trim());
    if (keys.length === 0) {
      toast.error("Please enter at least one API key");
      return;
    }

    setLoading(true);
    try {
      for (const [provider, key] of keys) {
        if (key.trim()) {
          await addProviderKey({
            workspaceId: workspaceId as any,
            provider: provider as any,
            label: `${provider.toUpperCase()} API Key`,
            last4: key.slice(-4),
          });
        }
      }

      toast.success("API keys configured!");
      setCurrentStep("complete");
      
      // Clean up session storage
      sessionStorage.removeItem("setupWorkspaceId");
      sessionStorage.removeItem("setupBotAccountId");
      sessionStorage.removeItem("setupAgentProfileId");
    } catch (error) {
      toast.error("Failed to configure API keys");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const renderWorkspaceStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Create Your Workspace</h2>
        <p className="text-gray-600 mt-2">
          Set up your AskBuddy workspace to get started
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="workspaceName">Workspace Name</Label>
          <Input
            id="workspaceName"
            placeholder="My AskBuddy Bot"
            value={workspaceData.name}
            onChange={(e) => setWorkspaceData({...workspaceData, name: e.target.value})}
          />
        </div>

        <div>
          <Label htmlFor="timezone">Timezone</Label>
          <Input
            id="timezone"
            placeholder="UTC"
            value={workspaceData.timezone}
            onChange={(e) => setWorkspaceData({...workspaceData, timezone: e.target.value})}
          />
        </div>
      </div>

      <Button 
        onClick={handleWorkspaceSubmit} 
        disabled={loading} 
        className="w-full"
      >
        {loading ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
        Continue
        <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </div>
  );

  const renderBotStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Configure Your Bot</h2>
        <p className="text-gray-600 mt-2">
          Connect your Twitter/X account and set bot settings
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="username">Bot Username (without @)</Label>
          <Input
            id="username"
            placeholder="AskBuddy"
            value={botData.username}
            onChange={(e) => setBotData({...botData, username: e.target.value})}
          />
        </div>

        <div>
          <Label htmlFor="xUserId">X/Twitter User ID</Label>
          <Input
            id="xUserId"
            placeholder="**********"
            value={botData.xUserId}
            onChange={(e) => setBotData({...botData, xUserId: e.target.value})}
          />
          <p className="text-sm text-gray-500 mt-1">
            Find your User ID at <a href="https://tweeterid.com" className="text-blue-500">tweeterid.com</a>
          </p>
        </div>

        <div>
          <Label htmlFor="displayName">Display Name (optional)</Label>
          <Input
            id="displayName"
            placeholder="AskBuddy Assistant"
            value={botData.displayName}
            onChange={(e) => setBotData({...botData, displayName: e.target.value})}
          />
        </div>

        <div>
          <Label htmlFor="twitterApiKey">Twitter API Key</Label>
          <Input
            id="twitterApiKey"
            type="password"
            placeholder="Your Twitter API key"
            value={botData.twitterApiKey}
            onChange={(e) => setBotData({...botData, twitterApiKey: e.target.value})}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="autoReply"
            checked={botData.autoReplyEnabled}
            onCheckedChange={(checked) => setBotData({...botData, autoReplyEnabled: !!checked})}
          />
          <Label htmlFor="autoReply">Enable auto-reply to mentions</Label>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="perMinuteLimit">Per-minute limit</Label>
            <Input
              id="perMinuteLimit"
              type="number"
              min="1"
              max="60"
              value={botData.perMinuteLimit}
              onChange={(e) => setBotData({...botData, perMinuteLimit: parseInt(e.target.value) || 5})}
            />
          </div>
          <div>
            <Label htmlFor="perDayLimit">Per-day limit</Label>
            <Input
              id="perDayLimit"
              type="number"
              min="1"
              max="1000"
              value={botData.perDayLimit}
              onChange={(e) => setBotData({...botData, perDayLimit: parseInt(e.target.value) || 100})}
            />
          </div>
        </div>
      </div>

      <Button 
        onClick={handleBotSubmit} 
        disabled={loading} 
        className="w-full"
      >
        {loading ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
        Continue
        <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </div>
  );

  const renderAgentStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Configure AI Agent</h2>
        <p className="text-gray-600 mt-2">
          Set up your bot's personality and capabilities
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="systemPrompt">System Prompt</Label>
          <textarea
            id="systemPrompt"
            className="w-full p-3 border rounded-md h-32 resize-none"
            value={agentData.systemPrompt}
            onChange={(e) => setAgentData({...agentData, systemPrompt: e.target.value})}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="temperature">Temperature</Label>
            <Input
              id="temperature"
              type="number"
              min="0"
              max="1"
              step="0.1"
              value={agentData.temperature}
              onChange={(e) => setAgentData({...agentData, temperature: parseFloat(e.target.value) || 0.7})}
            />
          </div>
          <div>
            <Label htmlFor="modelPrimary">AI Model</Label>
            <select
              id="modelPrimary"
              className="w-full p-2 border rounded-md"
              value={agentData.modelPrimary}
              onChange={(e) => setAgentData({...agentData, modelPrimary: e.target.value})}
            >
              <option value="openai/gpt-4o-mini">GPT-4o Mini</option>
              <option value="openai/gpt-4o">GPT-4o</option>
              <option value="anthropic/claude-3-haiku">Claude 3 Haiku</option>
              <option value="anthropic/claude-3-sonnet">Claude 3 Sonnet</option>
            </select>
          </div>
        </div>

        <div className="space-y-3">
          <Label>Enable Tools</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="enableExa"
                checked={agentData.enableExa}
                onCheckedChange={(checked) => setAgentData({...agentData, enableExa: !!checked})}
              />
              <Label htmlFor="enableExa">Exa Web Search</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="enableFirecrawl"
                checked={agentData.enableFirecrawl}
                onCheckedChange={(checked) => setAgentData({...agentData, enableFirecrawl: !!checked})}
              />
              <Label htmlFor="enableFirecrawl">Firecrawl Web Scraping</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="enableXAI"
                checked={agentData.enableXAI}
                onCheckedChange={(checked) => setAgentData({...agentData, enableXAI: !!checked})}
              />
              <Label htmlFor="enableXAI">xAI Live Search</Label>
            </div>
          </div>
        </div>
      </div>

      <Button 
        onClick={handleAgentSubmit} 
        disabled={loading} 
        className="w-full"
      >
        {loading ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
        Continue
        <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </div>
  );

  const renderKeysStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">API Keys</h2>
        <p className="text-gray-600 mt-2">
          Configure your AI and service API keys
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="openrouter">OpenRouter API Key</Label>
          <Input
            id="openrouter"
            type="password"
            placeholder="sk-or-..."
            value={keysData.openrouter}
            onChange={(e) => setKeysData({...keysData, openrouter: e.target.value})}
          />
          <p className="text-sm text-gray-500 mt-1">
            Get your key at <a href="https://openrouter.ai" className="text-blue-500">openrouter.ai</a>
          </p>
        </div>

        <div>
          <Label htmlFor="exa">Exa API Key (optional)</Label>
          <Input
            id="exa"
            type="password"
            placeholder="exa_..."
            value={keysData.exa}
            onChange={(e) => setKeysData({...keysData, exa: e.target.value})}
          />
        </div>

        <div>
          <Label htmlFor="firecrawl">Firecrawl API Key (optional)</Label>
          <Input
            id="firecrawl"
            type="password"
            placeholder="fc-..."
            value={keysData.firecrawl}
            onChange={(e) => setKeysData({...keysData, firecrawl: e.target.value})}
          />
        </div>

        <div>
          <Label htmlFor="xai">xAI API Key (optional)</Label>
          <Input
            id="xai"
            type="password"
            placeholder="xai-..."
            value={keysData.xai}
            onChange={(e) => setKeysData({...keysData, xai: e.target.value})}
          />
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-yellow-800">Important:</p>
            <p className="text-yellow-700 mt-1">
              API keys are stored securely. Only the last 4 characters are shown in the interface for security.
            </p>
          </div>
        </div>
      </div>

      <Button 
        onClick={handleKeysSubmit} 
        disabled={loading} 
        className="w-full"
      >
        {loading ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
        Complete Setup
        <ArrowRight className="w-4 h-4 ml-2" />
      </Button>
    </div>
  );

  const renderCompleteStep = () => (
    <div className="space-y-6 text-center">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      
      <div>
        <h2 className="text-2xl font-bold text-green-800">Setup Complete!</h2>
        <p className="text-gray-600 mt-2">
          Your AskBuddy bot is now configured and ready to start responding to mentions.
        </p>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="font-medium text-green-800 mb-2">Next Steps:</h3>
        <ul className="text-sm text-green-700 space-y-1 text-left">
          <li>• Configure your Twitter webhook to receive mentions</li>
          <li>• Set up scheduled jobs for polling mentions</li>
          <li>• Test your bot with a mention</li>
          <li>• Monitor activity in the dashboard</li>
        </ul>
      </div>

      <div className="flex gap-4">
        <Button 
          onClick={() => window.location.href = "/admin"}
          className="flex-1"
        >
          Go to Dashboard
        </Button>
        <Button 
          onClick={() => window.location.href = "/admin/bot"}
          variant="outline"
          className="flex-1"
        >
          Configure Bot
        </Button>
      </div>
    </div>
  );

  return (
    <div className="p-8 max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const StepIcon = step.icon;
            const isActive = step.id === currentStep;
            const isCompleted = steps.findIndex(s => s.id === currentStep) > index;
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 
                  ${isCompleted ? 'bg-green-500 border-green-500 text-white' :
                    isActive ? 'bg-blue-500 border-blue-500 text-white' :
                    'bg-gray-100 border-gray-300 text-gray-500'
                  }
                `}>
                  <StepIcon className="w-5 h-5" />
                </div>
                <div className="ml-3">
                  <div className={`text-sm font-medium ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 ml-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-8">
          {currentStep === "workspace" && renderWorkspaceStep()}
          {currentStep === "bot" && renderBotStep()}
          {currentStep === "agent" && renderAgentStep()}
          {currentStep === "keys" && renderKeysStep()}
          {currentStep === "complete" && renderCompleteStep()}
        </CardContent>
      </Card>
    </div>
  );
}