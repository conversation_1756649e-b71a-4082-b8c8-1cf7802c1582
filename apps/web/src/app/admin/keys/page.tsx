"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@AskBuddy/backend";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Key, 
  Plus,
  Trash2,
  Eye,
  EyeOff,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Shield,
  Loader2,
  Copy,
  Globe,
  Brain,
  Search
} from "lucide-react";
import { toast } from "sonner";

const API_PROVIDERS = [
  {
    id: "openrouter",
    name: "OpenRouter",
    description: "Access to multiple AI models (GPT, Claude, etc.)",
    icon: Brain,
    color: "text-blue-500",
    bgColor: "bg-blue-50",
    website: "https://openrouter.ai",
    keyPattern: /^sk-or-/,
    required: true,
  },
  {
    id: "exa",
    name: "Exa",
    description: "Web search API for finding relevant content",
    icon: Search,
    color: "text-green-500",
    bgColor: "bg-green-50",
    website: "https://exa.ai",
    keyPattern: /^[a-f0-9-]+$/,
    required: false,
  },
  {
    id: "firecrawl",
    name: "Firecrawl",
    description: "Web scraping and content extraction",
    icon: Globe,
    color: "text-orange-500",
    bgColor: "bg-orange-50",
    website: "https://firecrawl.dev",
    keyPattern: /^fc-/,
    required: false,
  },
  {
    id: "xai_live_search",
    name: "xAI",
    description: "Live search for Twitter/X content",
    icon: Search,
    color: "text-purple-500",
    bgColor: "bg-purple-50",
    website: "https://x.ai",
    keyPattern: /^xai-/,
    required: false,
  },
  {
    id: "twitterapi_io",
    name: "Twitter API",
    description: "Twitter/X integration via docs.twitterapi.io",
    icon: Key,
    color: "text-blue-400",
    bgColor: "bg-blue-50",
    website: "https://docs.twitterapi.io",
    keyPattern: /^.{8,}$/,
    required: true,
  },
] as const;

export default function APIKeysPage() {
  const [showKey, setShowKey] = useState<Record<string, boolean>>({});
  const [newKeys, setNewKeys] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  // Get workspaces and provider keys
  const workspaces = useQuery(api.admin.getWorkspaces);
  const workspace = workspaces?.[0];
  
  const providerKeys = useQuery(
    api.admin.getProviderKeys,
    workspace ? { workspaceId: workspace._id } : "skip"
  );

  const addProviderKey = useMutation(api.admin.addProviderKey);

  const handleAddKey = async (providerId: string) => {
    const keyValue = newKeys[providerId]?.trim();
    if (!keyValue) {
      toast.error("Please enter an API key");
      return;
    }

    if (!workspace) {
      toast.error("No workspace found");
      return;
    }

    const provider = API_PROVIDERS.find(p => p.id === providerId);
    if (!provider) return;

    if (!provider.keyPattern.test(keyValue)) {
      toast.error(`Invalid API key format for ${provider.name}`);
      return;
    }

    setLoading(prev => ({ ...prev, [providerId]: true }));
    try {
      await addProviderKey({
        workspaceId: workspace._id as any,
        provider: providerId as any,
        label: `${provider.name} API Key`,
        last4: keyValue.slice(-4),
      });

      setNewKeys(prev => ({ ...prev, [providerId]: "" }));
      toast.success(`${provider.name} API key added successfully!`);
    } catch (error) {
      toast.error(`Failed to add ${provider.name} API key`);
      console.error(error);
    } finally {
      setLoading(prev => ({ ...prev, [providerId]: false }));
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const getKeyStatus = (providerId: string) => {
    return providerKeys?.find(key => key.provider === providerId);
  };

  // Loading state
  if (!workspaces) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // No workspace state
  if (workspaces.length === 0) {
    return (
      <div className="p-8 max-w-2xl mx-auto">
        <Card>
          <CardContent className="p-8 text-center">
            <Key className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">No Workspace Found</h2>
            <p className="text-gray-600 mb-6">
              Please create a workspace first to manage API keys.
            </p>
            <Button onClick={() => window.location.href = "/admin/setup"}>
              Create Workspace
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const requiredKeys = API_PROVIDERS.filter(p => p.required);
  const optionalKeys = API_PROVIDERS.filter(p => !p.required);
  const configuredCount = API_PROVIDERS.filter(p => getKeyStatus(p.id)).length;

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          API Keys Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Configure API keys for AI models and external services
        </p>
        
        <div className="mt-4 flex items-center space-x-4">
          <Badge variant="outline" className="px-3 py-1">
            {configuredCount} of {API_PROVIDERS.length} configured
          </Badge>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Active</span>
            <div className="w-3 h-3 bg-gray-400 rounded-full ml-4"></div>
            <span className="text-sm text-gray-600">Not configured</span>
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <Card className="mb-8 border-blue-200 bg-blue-50 dark:bg-blue-950">
        <CardContent className="p-6">
          <div className="flex items-start">
            <Shield className="h-6 w-6 text-blue-500 mr-3 mt-0.5" />
            <div>
              <h3 className="font-semibold text-blue-800 dark:text-blue-200">
                Security Notice
              </h3>
              <p className="text-blue-700 dark:text-blue-300 mt-1 text-sm">
                API keys are stored securely and only the last 4 characters are displayed. 
                Keys are encrypted and never exposed in logs or client-side code.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Required Keys */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          Required API Keys
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {requiredKeys.map((provider) => {
            const ProviderIcon = provider.icon;
            const keyStatus = getKeyStatus(provider.id);
            const isLoading = loading[provider.id];

            return (
              <Card key={provider.id} className={`border-2 ${keyStatus ? 'border-green-200' : 'border-red-200'}`}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${provider.bgColor} mr-3`}>
                        <ProviderIcon className={`h-6 w-6 ${provider.color}`} />
                      </div>
                      <div>
                        <h3 className="font-semibold">{provider.name}</h3>
                        <p className="text-sm text-gray-500 font-normal">
                          {provider.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {keyStatus ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {keyStatus ? (
                      <div className="bg-green-50 dark:bg-green-950 border border-green-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-green-800 dark:text-green-200">
                              {keyStatus.label}
                            </p>
                            <p className="text-sm text-green-600 dark:text-green-400">
                              ••••••••{keyStatus.last4}
                            </p>
                            <p className="text-xs text-green-500 mt-1">
                              Added {new Date(keyStatus.created_at_ms).toLocaleDateString()}
                            </p>
                          </div>
                          <Badge className="bg-green-100 text-green-800">
                            Active
                          </Badge>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor={`key-${provider.id}`}>
                            {provider.name} API Key
                          </Label>
                          <div className="flex space-x-2 mt-1">
                            <div className="relative flex-1">
                              <Input
                                id={`key-${provider.id}`}
                                type={showKey[provider.id] ? "text" : "password"}
                                placeholder={`Enter your ${provider.name} API key...`}
                                value={newKeys[provider.id] || ""}
                                onChange={(e) => setNewKeys(prev => ({
                                  ...prev,
                                  [provider.id]: e.target.value,
                                }))}
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-auto p-1"
                                onClick={() => setShowKey(prev => ({
                                  ...prev,
                                  [provider.id]: !prev[provider.id],
                                }))}
                              >
                                {showKey[provider.id] ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                            <Button
                              onClick={() => handleAddKey(provider.id)}
                              disabled={isLoading || !newKeys[provider.id]?.trim()}
                            >
                              {isLoading ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                              ) : (
                                <Plus className="w-4 h-4" />
                              )}
                            </Button>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <a
                            href={provider.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-500 hover:text-blue-700 flex items-center"
                          >
                            Get API Key
                            <ExternalLink className="w-3 h-3 ml-1" />
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Optional Keys */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
          Optional API Keys
        </h2>
        <p className="text-gray-600 mb-4 text-sm">
          These keys enhance your bot's capabilities but are not required for basic functionality.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {optionalKeys.map((provider) => {
            const ProviderIcon = provider.icon;
            const keyStatus = getKeyStatus(provider.id);
            const isLoading = loading[provider.id];

            return (
              <Card key={provider.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${provider.bgColor} mr-3`}>
                        <ProviderIcon className={`h-6 w-6 ${provider.color}`} />
                      </div>
                      <div>
                        <h3 className="font-semibold">{provider.name}</h3>
                        <p className="text-sm text-gray-500 font-normal">
                          {provider.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {keyStatus ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <div className="h-5 w-5 bg-gray-300 rounded-full" />
                      )}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {keyStatus ? (
                      <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium">
                              {keyStatus.label}
                            </p>
                            <p className="text-sm text-gray-600">
                              ••••••••{keyStatus.last4}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              Added {new Date(keyStatus.created_at_ms).toLocaleDateString()}
                            </p>
                          </div>
                          <Badge variant="secondary">
                            Configured
                          </Badge>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor={`key-${provider.id}`}>
                            {provider.name} API Key
                          </Label>
                          <div className="flex space-x-2 mt-1">
                            <div className="relative flex-1">
                              <Input
                                id={`key-${provider.id}`}
                                type={showKey[provider.id] ? "text" : "password"}
                                placeholder={`Enter your ${provider.name} API key...`}
                                value={newKeys[provider.id] || ""}
                                onChange={(e) => setNewKeys(prev => ({
                                  ...prev,
                                  [provider.id]: e.target.value,
                                }))}
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-auto p-1"
                                onClick={() => setShowKey(prev => ({
                                  ...prev,
                                  [provider.id]: !prev[provider.id],
                                }))}
                              >
                                {showKey[provider.id] ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                            <Button
                              variant="outline"
                              onClick={() => handleAddKey(provider.id)}
                              disabled={isLoading || !newKeys[provider.id]?.trim()}
                            >
                              {isLoading ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                              ) : (
                                <Plus className="w-4 h-4" />
                              )}
                            </Button>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <a
                            href={provider.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-500 hover:text-blue-700 flex items-center"
                          >
                            Get API Key
                            <ExternalLink className="w-3 h-3 ml-1" />
                          </a>
                          <Badge variant="outline" className="text-xs">
                            Optional
                          </Badge>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Environment Variables Info */}
      <Card className="mt-8 border-gray-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Copy className="w-5 h-5 mr-2" />
            Environment Variables
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">
            API keys are stored in the database but also need to be set as environment variables 
            for the backend to access them:
          </p>
          <div className="bg-gray-50 dark:bg-gray-800 rounded p-4 font-mono text-sm space-y-2">
            {API_PROVIDERS.map(provider => {
              const envVar = provider.id.toUpperCase() + "_API_KEY";
              return (
                <div key={provider.id} className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">
                    {envVar}=your_key_here
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(envVar, "Environment variable name")}
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
              );
            })}
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Add these to your .env.local file in the backend package.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}