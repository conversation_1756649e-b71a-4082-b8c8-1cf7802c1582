"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@AskBuddy/backend";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MessageSquare,
  Search,
  Filter,
  ExternalLink,
  Clock,
  User,
  Hash,
  Bot,
  Plus
} from "lucide-react";

export default function MentionsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterProcessed, setFilterProcessed] = useState<"all" | "processed" | "unprocessed">("all");

  // Get workspaces
  const workspaces = useQuery(api.admin.getWorkspaces);
  const workspace = workspaces?.[0];

  // For now, we'll show placeholder since mention_events queries aren't exposed in admin.ts
  // In a real implementation, you'd add queries to fetch mentions
  const mentions: any[] = []; // TODO: Add real query when available

  const filteredMentions = mentions.filter((mention) => {
    const matchesSearch = 
      mention.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mention.author_username.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = 
      filterProcessed === "all" ||
      (filterProcessed === "processed" && mention.processed) ||
      (filterProcessed === "unprocessed" && !mention.processed);

    return matchesSearch && matchesFilter;
  });

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ago`;
    }
    return `${minutes}m ago`;
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Mentions
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Monitor and track Twitter mentions of your bot
        </p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search mentions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={filterProcessed === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilterProcessed("all")}
          >
            All
          </Button>
          <Button
            variant={filterProcessed === "processed" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilterProcessed("processed")}
          >
            Processed
          </Button>
          <Button
            variant={filterProcessed === "unprocessed" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilterProcessed("unprocessed")}
          >
            Pending
          </Button>
        </div>
      </div>

      {/* Mentions List */}
      <div className="space-y-4">
        {filteredMentions.map((mention) => (
          <Card key={mention.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="font-medium text-gray-900 dark:text-white">
                      @{mention.author_username}
                    </span>
                    <Badge variant={mention.processed ? "default" : "secondary"}>
                      {mention.processed ? "Processed" : "Pending"}
                    </Badge>
                    <Badge variant="outline">
                      {mention.source}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500 ml-auto">
                      <Clock className="h-4 w-4 mr-1" />
                      {formatTimeAgo(mention.created_at_ms)}
                    </div>
                  </div>

                  <p className="text-gray-900 dark:text-white mb-3 leading-relaxed">
                    {mention.text}
                  </p>

                  {mention.urls.length > 0 && (
                    <div className="mb-3">
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                        URLs:
                      </p>
                      <div className="space-y-1">
                        {mention.urls.map((url, index) => (
                          <a
                            key={index}
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            {url}
                          </a>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Hash className="h-3 w-3 mr-1" />
                      {mention.tweet_id}
                    </div>
                    <div className="flex items-center">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      {mention.lang?.toUpperCase()}
                    </div>
                  </div>
                </div>

                <div className="flex gap-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(`https://twitter.com/i/status/${mention.tweet_id}`, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View Tweet
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {!workspaces || workspaces.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No Workspace Found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Create a workspace and configure your bot to start receiving mentions.
              </p>
              <Button onClick={() => window.location.href = "/admin/setup"}>
                <Plus className="w-4 h-4 mr-2" />
                Setup Bot
              </Button>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No mentions yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Your bot hasn't received any mentions yet. Once configured with webhooks or polling, mentions will appear here.
              </p>
              <div className="flex gap-3 justify-center">
                <Button onClick={() => window.location.href = "/admin/bot"} variant="outline">
                  Configure Webhooks
                </Button>
                <Button onClick={() => window.location.href = "/admin/setup"}>
                  Complete Setup
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}