"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@AskBuddy/backend";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Plus,
  Trash2,
  UserX,
  MessageSquareX,
  UserCheck,
  Loader2,
  AlertTriangle
} from "lucide-react";
import { toast } from "sonner";

const RULE_TYPES = [
  {
    id: "block_user",
    name: "Block User",
    description: "Block replies to specific users",
    icon: UserX,
    color: "text-red-500",
    bgColor: "bg-red-50",
    placeholder: "username or user_id",
  },
  {
    id: "block_keyword",
    name: "Block Keyword",
    description: "Block replies containing specific keywords",
    icon: MessageSquareX,
    color: "text-orange-500",
    bgColor: "bg-orange-50",
    placeholder: "keyword or phrase",
  },
  {
    id: "allow_user",
    name: "Allow User",
    description: "Always allow replies to specific users",
    icon: UserCheck,
    color: "text-green-500",
    bgColor: "bg-green-50",
    placeholder: "username or user_id",
  },
] as const;

export default function PolicyRulesPage() {
  const [selectedType, setSelectedType] = useState<"block_user" | "block_keyword" | "allow_user">("block_user");
  const [newRule, setNewRule] = useState({ value: "", notes: "" });
  const [loading, setLoading] = useState(false);

  // Get workspaces and policy rules
  const workspaces = useQuery(api.admin.getWorkspaces);
  const workspace = workspaces?.[0];
  
  const policyRules = useQuery(
    api.admin.getPolicyRules,
    workspace ? { workspaceId: workspace._id } : "skip"
  );

  const addPolicyRule = useMutation(api.admin.addPolicyRule);
  const removePolicyRule = useMutation(api.admin.removePolicyRule);

  const handleAddRule = async () => {
    if (!newRule.value.trim()) {
      toast.error("Please enter a value for the rule");
      return;
    }

    if (!workspace) {
      toast.error("No workspace found");
      return;
    }

    setLoading(true);
    try {
      await addPolicyRule({
        workspaceId: workspace._id as any,
        ruleType: selectedType,
        value: newRule.value.trim(),
        notes: newRule.notes.trim() || undefined,
      });

      setNewRule({ value: "", notes: "" });
      toast.success("Policy rule added successfully!");
    } catch (error) {
      toast.error("Failed to add policy rule");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveRule = async (ruleId: string) => {
    setLoading(true);
    try {
      await removePolicyRule({
        ruleId: ruleId as any,
      });
      toast.success("Policy rule removed successfully!");
    } catch (error) {
      toast.error("Failed to remove policy rule");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getRulesByType = (type: string) => {
    return policyRules?.filter(rule => rule.ruleType === type) || [];
  };

  // Loading state
  if (!workspaces) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // No workspace state
  if (workspaces.length === 0) {
    return (
      <div className="p-8 max-w-2xl mx-auto">
        <Card>
          <CardContent className="p-8 text-center">
            <Shield className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">No Workspace Found</h2>
            <p className="text-gray-600 mb-6">
              Please create a workspace first to manage policy rules.
            </p>
            <Button onClick={() => window.location.href = "/admin/setup"}>
              Create Workspace
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const selectedRuleType = RULE_TYPES.find(type => type.id === selectedType);

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Policy Rules
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage content moderation and user access rules for your bot
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Add Rule Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Plus className="w-5 h-5 mr-2" />
              Add New Rule
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Rule Type</Label>
              <div className="mt-2 space-y-2">
                {RULE_TYPES.map((type) => {
                  const TypeIcon = type.icon;
                  const isSelected = selectedType === type.id;
                  
                  return (
                    <div
                      key={type.id}
                      onClick={() => setSelectedType(type.id)}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        isSelected
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center">
                        <div className={`p-2 rounded ${type.bgColor} mr-3`}>
                          <TypeIcon className={`h-4 w-4 ${type.color}`} />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{type.name}</p>
                          <p className="text-xs text-gray-500">{type.description}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {selectedRuleType && (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="ruleValue">
                    {selectedRuleType.name} Value
                  </Label>
                  <Input
                    id="ruleValue"
                    placeholder={selectedRuleType.placeholder}
                    value={newRule.value}
                    onChange={(e) => setNewRule({ ...newRule, value: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="ruleNotes">Notes (optional)</Label>
                  <Input
                    id="ruleNotes"
                    placeholder="Add a note about this rule..."
                    value={newRule.notes}
                    onChange={(e) => setNewRule({ ...newRule, notes: e.target.value })}
                  />
                </div>

                <Button 
                  onClick={handleAddRule}
                  disabled={loading || !newRule.value.trim()}
                  className="w-full"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <Plus className="w-4 h-4 mr-2" />
                  )}
                  Add Rule
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Rule Lists */}
        <div className="lg:col-span-2 space-y-6">
          {RULE_TYPES.map((type) => {
            const TypeIcon = type.icon;
            const rules = getRulesByType(type.id);
            
            return (
              <Card key={type.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded ${type.bgColor} mr-3`}>
                        <TypeIcon className={`h-5 w-5 ${type.color}`} />
                      </div>
                      <div>
                        <h3>{type.name}</h3>
                        <p className="text-sm text-gray-500 font-normal">
                          {type.description}
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">
                      {rules.length} {rules.length === 1 ? 'rule' : 'rules'}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {rules.length > 0 ? (
                    <div className="space-y-3">
                      {rules.map((rule) => (
                        <div
                          key={rule._id}
                          className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                        >
                          <div>
                            <p className="font-medium">{rule.value}</p>
                            {rule.notes && (
                              <p className="text-sm text-gray-500 mt-1">
                                {rule.notes}
                              </p>
                            )}
                            <p className="text-xs text-gray-400 mt-1">
                              Added {new Date(rule.created_at_ms).toLocaleDateString()}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveRule(rule._id)}
                            disabled={loading}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <TypeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No {type.name.toLowerCase()} rules</p>
                      <p className="text-sm text-gray-500 mt-1">
                        Add rules to control bot behavior
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Rule Information */}
      <Card className="mt-8 border-yellow-200 bg-yellow-50 dark:bg-yellow-950">
        <CardContent className="p-6">
          <div className="flex items-start">
            <AlertTriangle className="h-6 w-6 text-yellow-500 mr-3 mt-0.5" />
            <div>
              <h3 className="font-semibold text-yellow-800 dark:text-yellow-200">
                How Policy Rules Work
              </h3>
              <div className="text-yellow-700 dark:text-yellow-300 mt-2 space-y-2 text-sm">
                <p>
                  <strong>Block User:</strong> Bot will skip mentions from these users (username or user ID)
                </p>
                <p>
                  <strong>Block Keyword:</strong> Bot will skip mentions containing these keywords or phrases
                </p>
                <p>
                  <strong>Allow User:</strong> Bot will always respond to these users, bypassing other blocks
                </p>
                <p className="mt-3 pt-2 border-t border-yellow-300">
                  <strong>Priority:</strong> Allow rules take precedence over block rules. 
                  Rules are checked before AI classification.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}