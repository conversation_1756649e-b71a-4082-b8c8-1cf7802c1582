"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@AskBuddy/backend";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { 
  Bot, 
  Settings, 
  Save,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Loader2
} from "lucide-react";
import { toast } from "sonner";

export default function BotSettingsPage() {
  const [selectedBotId, setSelectedBotId] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const [loading, setLoading] = useState(false);

  // Get workspaces and bots
  const workspaces = useQuery(api.admin.getWorkspaces);
  const workspace = workspaces?.[0];
  
  const botAccounts = useQuery(
    api.admin.getBotAccounts,
    workspace ? { workspaceId: workspace._id } : "skip"
  );

  const updateBotAccount = useMutation(api.admin.updateBotAccount);

  const selectedBot = botAccounts?.find(bot => bot._id === selectedBotId);

  const [botSettings, setBotSettings] = useState({
    autoReplyEnabled: selectedBot?.auto_reply_enabled ?? true,
    perMinuteLimit: selectedBot?.per_minute_limit ?? 5,
    perDayLimit: selectedBot?.per_day_limit ?? 100,
  });

  // Update settings when bot selection changes
  useState(() => {
    if (selectedBot) {
      setBotSettings({
        autoReplyEnabled: selectedBot.auto_reply_enabled,
        perMinuteLimit: selectedBot.per_minute_limit,
        perDayLimit: selectedBot.per_day_limit,
      });
    }
  }, [selectedBot]);

  const handleSaveSettings = async () => {
    if (!selectedBotId) {
      toast.error("No bot selected");
      return;
    }

    setLoading(true);
    try {
      await updateBotAccount({
        botAccountId: selectedBotId as any,
        autoReplyEnabled: botSettings.autoReplyEnabled,
        perMinuteLimit: botSettings.perMinuteLimit,
        perDayLimit: botSettings.perDayLimit,
      });

      toast.success("Bot settings updated successfully!");
    } catch (error) {
      toast.error("Failed to update bot settings");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (!workspaces) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // No workspace state
  if (workspaces.length === 0) {
    return (
      <div className="p-8 max-w-2xl mx-auto">
        <Card>
          <CardContent className="p-8 text-center">
            <Bot className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">No Workspace Found</h2>
            <p className="text-gray-600 mb-6">
              Please create a workspace first to manage bot accounts.
            </p>
            <Button onClick={() => window.location.href = "/admin/setup"}>
              <Plus className="w-5 h-5 mr-2" />
              Create Workspace
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Bot Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Configure your Twitter bot accounts and their behavior
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Bot List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Bot Accounts
              <Button 
                size="sm" 
                onClick={() => window.location.href = "/admin/setup"}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {botAccounts && botAccounts.length > 0 ? (
              <div className="space-y-3">
                {botAccounts.map((bot) => (
                  <div
                    key={bot._id}
                    onClick={() => setSelectedBotId(bot._id)}
                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                      selectedBotId === bot._id
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Bot className="h-6 w-6 text-blue-600 mr-3" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            @{bot.username}
                          </p>
                          <p className="text-sm text-gray-500">
                            ID: {bot.x_user_id}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <div
                          className={`w-3 h-3 rounded-full ${
                            bot.auto_reply_enabled ? "bg-green-500" : "bg-gray-400"
                          }`}
                        />
                        <Badge
                          variant={bot.auto_reply_enabled ? "default" : "secondary"}
                        >
                          {bot.auto_reply_enabled ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                    </div>
                    {bot.display_name && (
                      <p className="text-sm text-gray-600 mt-2">
                        {bot.display_name}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No bot accounts configured</p>
                <Button 
                  className="mt-4"
                  onClick={() => window.location.href = "/admin/setup"}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Bot
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Bot Settings */}
        <div className="lg:col-span-2 space-y-6">
          {selectedBot ? (
            <>
              {/* Bot Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="w-5 h-5 mr-2" />
                    Bot Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Username</Label>
                      <div className="flex items-center mt-1">
                        <span className="text-lg font-mono">@{selectedBot.username}</span>
                        <a
                          href={`https://twitter.com/${selectedBot.username}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-500 hover:text-blue-700"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </div>
                    </div>
                    <div>
                      <Label>X User ID</Label>
                      <p className="text-lg font-mono mt-1">{selectedBot.x_user_id}</p>
                    </div>
                  </div>
                  
                  {selectedBot.display_name && (
                    <div>
                      <Label>Display Name</Label>
                      <p className="text-lg mt-1">{selectedBot.display_name}</p>
                    </div>
                  )}

                  <div>
                    <Label>API Provider</Label>
                    <p className="text-sm text-gray-600 mt-1">
                      {selectedBot.credentials.provider} 
                      {selectedBot.credentials.key_label && ` (${selectedBot.credentials.key_label})`}
                      {selectedBot.credentials.key_last4 && ` •••• ${selectedBot.credentials.key_last4}`}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Bot Behavior Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Bot Behavior</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>Auto-Reply to Mentions</Label>
                      <p className="text-sm text-gray-500">
                        Automatically respond to Twitter mentions
                      </p>
                    </div>
                    <Checkbox
                      checked={botSettings.autoReplyEnabled}
                      onCheckedChange={(checked) =>
                        setBotSettings({
                          ...botSettings,
                          autoReplyEnabled: !!checked,
                        })
                      }
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="perMinuteLimit">Per-Minute Limit</Label>
                      <Input
                        id="perMinuteLimit"
                        type="number"
                        min="1"
                        max="60"
                        value={botSettings.perMinuteLimit}
                        onChange={(e) =>
                          setBotSettings({
                            ...botSettings,
                            perMinuteLimit: parseInt(e.target.value) || 1,
                          })
                        }
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Maximum replies per minute
                      </p>
                    </div>
                    <div>
                      <Label htmlFor="perDayLimit">Per-Day Limit</Label>
                      <Input
                        id="perDayLimit"
                        type="number"
                        min="1"
                        max="1000"
                        value={botSettings.perDayLimit}
                        onChange={(e) =>
                          setBotSettings({
                            ...botSettings,
                            perDayLimit: parseInt(e.target.value) || 1,
                          })
                        }
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Maximum replies per day
                      </p>
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex">
                      <AlertTriangle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                      <div className="text-sm">
                        <p className="font-medium text-yellow-800">Rate Limits</p>
                        <p className="text-yellow-700 mt-1">
                          These limits help prevent your bot from being rate-limited by Twitter. 
                          Adjust based on your Twitter API plan.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button 
                      onClick={handleSaveSettings}
                      disabled={loading}
                    >
                      {loading ? (
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      ) : (
                        <Save className="w-4 h-4 mr-2" />
                      )}
                      Save Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Webhook Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>Webhook Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Webhook URL</Label>
                      <div className="flex items-center mt-1">
                        <Input
                          readOnly
                          value={`${window.location.origin}/api/twitter/webhook`}
                          className="font-mono text-sm"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            navigator.clipboard.writeText(`${window.location.origin}/api/twitter/webhook`);
                            toast.success("Webhook URL copied!");
                          }}
                          className="ml-2"
                        >
                          Copy
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <Label>Required Headers</Label>
                      <div className="bg-gray-50 dark:bg-gray-800 rounded p-3 font-mono text-sm mt-1">
                        <div>Content-Type: application/json</div>
                        <div>x-webhook-secret: [YOUR_WEBHOOK_SECRET]</div>
                      </div>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex">
                        <CheckCircle className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
                        <div className="text-sm">
                          <p className="font-medium text-blue-800">Setup Instructions</p>
                          <p className="text-blue-700 mt-1">
                            Configure this webhook URL in your Twitter API dashboard at{" "}
                            <a
                              href="https://docs.twitterapi.io"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="underline"
                            >
                              docs.twitterapi.io
                            </a>{" "}
                            to receive mentions in real-time.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Settings className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Select a Bot Account</h3>
                <p className="text-gray-600">
                  Choose a bot account from the list to configure its settings
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}