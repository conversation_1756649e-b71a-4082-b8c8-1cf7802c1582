"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@AskBuddy/backend";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Zap, 
  Settings, 
  Save,
  Brain,
  Sliders,
  Globe,
  FileText,
  Search,
  Loader2,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from "lucide-react";
import { toast } from "sonner";

export default function AISettingsPage() {
  const [loading, setLoading] = useState(false);
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null);

  // Get workspaces and agent profiles
  const workspaces = useQuery(api.admin.getWorkspaces);
  const workspace = workspaces?.[0];
  
  const agentProfiles = useQuery(
    api.admin.getAgentProfiles,
    workspace ? { workspaceId: workspace._id } : "skip"
  );

  const updateAgentProfile = useMutation(api.admin.updateAgentProfile);
  const createDefaultAgentProfile = useMutation(api.admin.createDefaultAgentProfile);

  const selectedProfile = agentProfiles?.find(profile => profile._id === selectedProfileId);

  const [agentSettings, setAgentSettings] = useState({
    systemPrompt: "",
    temperature: 0.7,
    topP: 0.9,
    modelPrimary: "openai/gpt-4o-mini",
    modelFallbacks: ["openrouter/auto", "openai/gpt-4o-mini"],
    toolDefaults: {
      firecrawl_enabled: true,
      exa_enabled: true,
      xai_live_search_enabled: true,
      xai_search_max_results: 5,
      safe_search_web_news: true,
    },
  });

  // Update settings when profile selection changes
  useEffect(() => {
    if (selectedProfile) {
      setAgentSettings({
        systemPrompt: selectedProfile.system_prompt || "",
        temperature: selectedProfile.temperature || 0.7,
        topP: selectedProfile.top_p || 0.9,
        modelPrimary: selectedProfile.model_primary || "openai/gpt-4o-mini",
        modelFallbacks: selectedProfile.model_fallbacks || ["openrouter/auto", "openai/gpt-4o-mini"],
        toolDefaults: selectedProfile.tool_defaults || {
          firecrawl_enabled: true,
          exa_enabled: true,
          xai_live_search_enabled: true,
          xai_search_max_results: 5,
          safe_search_web_news: true,
        },
      });
    }
  }, [selectedProfile]);

  // Auto-select first profile
  useEffect(() => {
    if (agentProfiles && agentProfiles.length > 0 && !selectedProfileId) {
      setSelectedProfileId(agentProfiles[0]._id);
    }
  }, [agentProfiles, selectedProfileId]);

  const handleCreateDefaultProfile = async () => {
    if (!workspace) return;
    
    setLoading(true);
    try {
      const profileId = await createDefaultAgentProfile({
        workspaceId: workspace._id,
      });
      setSelectedProfileId(profileId);
      toast.success("Default agent profile created!");
    } catch (error) {
      toast.error("Failed to create agent profile");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!selectedProfileId) {
      toast.error("No profile selected");
      return;
    }

    setLoading(true);
    try {
      await updateAgentProfile({
        profileId: selectedProfileId as any,
        systemPrompt: agentSettings.systemPrompt,
        temperature: agentSettings.temperature,
        topP: agentSettings.topP,
        modelPrimary: agentSettings.modelPrimary,
        modelFallbacks: agentSettings.modelFallbacks,
        toolDefaults: agentSettings.toolDefaults,
      });

      toast.success("AI agent settings updated successfully!");
    } catch (error) {
      toast.error("Failed to update AI agent settings");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const resetToDefaults = () => {
    setAgentSettings({
      systemPrompt: `You are AskBuddy, a helpful Twitter bot that provides informative and accurate responses to mentions.

Your capabilities include:
- Searching the web for current information using Exa
- Scraping and analyzing web pages using Firecrawl
- Searching Twitter/X content using xAI Live Search
- Providing cited, well-researched answers

Guidelines:
- Always be helpful and informative
- Cite your sources when using external information
- Keep responses concise but comprehensive (under 280 characters when possible for Twitter)
- Be respectful and avoid controversial topics
- If you cannot find relevant information, say so honestly`,
      temperature: 0.7,
      topP: 0.9,
      modelPrimary: "openai/gpt-4o-mini",
      modelFallbacks: ["openrouter/auto", "openai/gpt-4o-mini"],
      toolDefaults: {
        firecrawl_enabled: true,
        exa_enabled: true,
        xai_live_search_enabled: true,
        xai_search_max_results: 5,
        safe_search_web_news: true,
      },
    });
    toast.info("Settings reset to defaults");
  };

  // Loading state
  if (!workspaces) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // No workspace state
  if (workspaces.length === 0) {
    return (
      <div className="p-8 max-w-2xl mx-auto">
        <Card>
          <CardContent className="p-8 text-center">
            <Brain className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">No Workspace Found</h2>
            <p className="text-gray-600 mb-6">
              Please create a workspace first to configure AI settings.
            </p>
            <Button onClick={() => window.location.href = "/admin/setup"}>
              Create Workspace
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          AI Agent Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Configure your bot's AI personality, models, and capabilities
        </p>
      </div>

      {/* Agent Profiles */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Agent Profiles</h2>
          {(!agentProfiles || agentProfiles.length === 0) && (
            <Button onClick={handleCreateDefaultProfile} disabled={loading}>
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Zap className="w-4 h-4 mr-2" />
              )}
              Create Default Profile
            </Button>
          )}
        </div>

        {agentProfiles && agentProfiles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            {agentProfiles.map((profile) => (
              <Card
                key={profile._id}
                className={`cursor-pointer transition-colors ${
                  selectedProfileId === profile._id
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                    : "hover:border-gray-300"
                }`}
                onClick={() => setSelectedProfileId(profile._id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Zap className="h-8 w-8 text-purple-600 mr-3" />
                      <div>
                        <p className="font-medium">{profile.name}</p>
                        <p className="text-sm text-gray-500">{profile.model_primary}</p>
                      </div>
                    </div>
                    {selectedProfileId === profile._id && (
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No AI agent profiles configured</p>
              <p className="text-sm text-gray-500 mt-1">
                Create a default profile to get started
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {selectedProfile && (
        <div className="space-y-6">
          {/* System Prompt */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                System Prompt
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="systemPrompt">
                    Personality and Instructions
                  </Label>
                  <textarea
                    id="systemPrompt"
                    className="w-full p-3 border rounded-md h-48 resize-y font-mono text-sm"
                    placeholder="Define your bot's personality, capabilities, and guidelines..."
                    value={agentSettings.systemPrompt}
                    onChange={(e) => setAgentSettings({
                      ...agentSettings,
                      systemPrompt: e.target.value,
                    })}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    This prompt defines how your bot behaves and responds to mentions. 
                    Be specific about tone, capabilities, and restrictions.
                  </p>
                </div>
                
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={resetToDefaults}
                    className="flex items-center"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Reset to Defaults
                  </Button>
                  <p className="text-sm text-gray-500 self-end">
                    {agentSettings.systemPrompt.length} characters
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Model Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="w-5 h-5 mr-2" />
                AI Model Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="modelPrimary">Primary Model</Label>
                  <select
                    id="modelPrimary"
                    className="w-full p-2 border rounded-md"
                    value={agentSettings.modelPrimary}
                    onChange={(e) => setAgentSettings({
                      ...agentSettings,
                      modelPrimary: e.target.value,
                    })}
                  >
                    <optgroup label="OpenAI">
                      <option value="openai/gpt-4o">GPT-4o</option>
                      <option value="openai/gpt-4o-mini">GPT-4o Mini</option>
                    </optgroup>
                    <optgroup label="Anthropic">
                      <option value="anthropic/claude-3-5-sonnet">Claude 3.5 Sonnet</option>
                      <option value="anthropic/claude-3-haiku">Claude 3 Haiku</option>
                    </optgroup>
                    <optgroup label="Google">
                      <option value="google/gemini-2.0-flash-exp">Gemini 2.0 Flash</option>
                      <option value="google/gemini-pro">Gemini Pro</option>
                    </optgroup>
                    <optgroup label="Meta">
                      <option value="meta-llama/llama-3.1-405b-instruct">Llama 3.1 405B</option>
                      <option value="meta-llama/llama-3.1-70b-instruct">Llama 3.1 70B</option>
                    </optgroup>
                  </select>
                </div>
                
                <div>
                  <Label>Fallback Models</Label>
                  <p className="text-sm text-gray-500">
                    Currently: {agentSettings.modelFallbacks.join(", ")}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="temperature">
                    Temperature ({agentSettings.temperature})
                  </Label>
                  <input
                    id="temperature"
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={agentSettings.temperature}
                    onChange={(e) => setAgentSettings({
                      ...agentSettings,
                      temperature: parseFloat(e.target.value),
                    })}
                    className="w-full mt-1"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Controls creativity. Lower = more focused, Higher = more creative
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="topP">
                    Top P ({agentSettings.topP})
                  </Label>
                  <input
                    id="topP"
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={agentSettings.topP}
                    onChange={(e) => setAgentSettings({
                      ...agentSettings,
                      topP: parseFloat(e.target.value),
                    })}
                    className="w-full mt-1"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Controls randomness. Lower = more deterministic
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tool Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Tool Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Search className="h-5 w-5 text-blue-500 mr-2" />
                      <div>
                        <Label>Exa Web Search</Label>
                        <p className="text-sm text-gray-500">
                          Search the web for current information
                        </p>
                      </div>
                    </div>
                    <Checkbox
                      checked={agentSettings.toolDefaults.exa_enabled}
                      onCheckedChange={(checked) => setAgentSettings({
                        ...agentSettings,
                        toolDefaults: {
                          ...agentSettings.toolDefaults,
                          exa_enabled: !!checked,
                        },
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Globe className="h-5 w-5 text-green-500 mr-2" />
                      <div>
                        <Label>Firecrawl Web Scraping</Label>
                        <p className="text-sm text-gray-500">
                          Scrape and analyze web pages
                        </p>
                      </div>
                    </div>
                    <Checkbox
                      checked={agentSettings.toolDefaults.firecrawl_enabled}
                      onCheckedChange={(checked) => setAgentSettings({
                        ...agentSettings,
                        toolDefaults: {
                          ...agentSettings.toolDefaults,
                          firecrawl_enabled: !!checked,
                        },
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Zap className="h-5 w-5 text-purple-500 mr-2" />
                      <div>
                        <Label>xAI Live Search</Label>
                        <p className="text-sm text-gray-500">
                          Search Twitter/X content
                        </p>
                      </div>
                    </div>
                    <Checkbox
                      checked={agentSettings.toolDefaults.xai_live_search_enabled}
                      onCheckedChange={(checked) => setAgentSettings({
                        ...agentSettings,
                        toolDefaults: {
                          ...agentSettings.toolDefaults,
                          xai_live_search_enabled: !!checked,
                        },
                      })}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="xaiMaxResults">
                      xAI Search Results ({agentSettings.toolDefaults.xai_search_max_results})
                    </Label>
                    <input
                      id="xaiMaxResults"
                      type="range"
                      min="1"
                      max="10"
                      step="1"
                      value={agentSettings.toolDefaults.xai_search_max_results}
                      onChange={(e) => setAgentSettings({
                        ...agentSettings,
                        toolDefaults: {
                          ...agentSettings.toolDefaults,
                          xai_search_max_results: parseInt(e.target.value),
                        },
                      })}
                      className="w-full mt-1"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Safe Search</Label>
                      <p className="text-sm text-gray-500">
                        Enable safe search for web and news
                      </p>
                    </div>
                    <Checkbox
                      checked={agentSettings.toolDefaults.safe_search_web_news}
                      onCheckedChange={(checked) => setAgentSettings({
                        ...agentSettings,
                        toolDefaults: {
                          ...agentSettings.toolDefaults,
                          safe_search_web_news: !!checked,
                        },
                      })}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-800">Tool Dependencies</p>
                    <p className="text-blue-700 mt-1">
                      Tools will only work if their corresponding API keys are configured. 
                      Check the API Keys page to ensure all required keys are set up.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button 
              onClick={handleSaveSettings}
              disabled={loading}
              size="lg"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Save AI Settings
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}