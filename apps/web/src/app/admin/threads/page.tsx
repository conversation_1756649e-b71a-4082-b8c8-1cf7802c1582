"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@AskBuddy/backend";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Bot,
  MessageSquare,
  ExternalLink,
  Clock,
  User,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Pause,
  Plus
} from "lucide-react";

export default function ThreadsPage() {
  const [filterStatus, setFilterStatus] = useState<"all" | "pending" | "responded" | "skipped" | "failed">("all");

  // Get workspaces
  const workspaces = useQuery(api.admin.getWorkspaces);
  const workspace = workspaces?.[0];

  // For now, we'll show placeholder since thread queries aren't exposed in admin.ts
  // In a real implementation, you'd add queries to fetch threads
  const threads: any[] = []; // TODO: Add real query when available

  const filteredThreads = threads.filter((thread) => {
    return filterStatus === "all" || thread.status === filterStatus;
  });

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ago`;
    }
    return `${minutes}m ago`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "responded":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "skipped":
        return <Pause className="h-4 w-4 text-gray-600" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "responded":
        return "default";
      case "pending":
        return "secondary";
      case "skipped":
        return "outline";
      case "failed":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Threads
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage conversation threads and their processing status
        </p>
      </div>

      {/* Status Filter */}
      <div className="flex gap-2 mb-6">
        <Button
          variant={filterStatus === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterStatus("all")}
        >
          All Threads
        </Button>
        <Button
          variant={filterStatus === "pending" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterStatus("pending")}
        >
          Pending
        </Button>
        <Button
          variant={filterStatus === "responded" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterStatus("responded")}
        >
          Responded
        </Button>
        <Button
          variant={filterStatus === "skipped" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterStatus("skipped")}
        >
          Skipped
        </Button>
        <Button
          variant={filterStatus === "failed" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilterStatus("failed")}
        >
          Failed
        </Button>
      </div>

      {/* Threads List */}
      <div className="space-y-4">
        {filteredThreads.map((thread) => (
          <Card key={thread.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  {getStatusIcon(thread.status)}
                  <Badge variant={getStatusColor(thread.status) as any}>
                    {thread.status.charAt(0).toUpperCase() + thread.status.slice(1)}
                  </Badge>
                  <Badge variant="outline">
                    {thread.decision.replace("_", " ")}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    Model: {thread.model}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  <Clock className="inline h-3 w-3 mr-1" />
                  {formatTimeAgo(thread.updated_at_ms)}
                </div>
              </div>

              {/* Original Mention */}
              <div className="border-l-4 border-blue-500 pl-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-900 dark:text-white">
                    @{thread.mention.author_username}
                  </span>
                  <span className="text-sm text-gray-500">mentioned:</span>
                </div>
                <p className="text-gray-900 dark:text-white">
                  {thread.mention.text}
                </p>
              </div>

              {/* Reply (if exists) */}
              {thread.reply && (
                <div className="border-l-4 border-green-500 pl-4 mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Bot className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-600">
                      AskBuddy replied:
                    </span>
                  </div>
                  <p className="text-gray-900 dark:text-white">
                    {thread.reply.text}
                  </p>
                </div>
              )}

              {/* Reason (if failed/skipped) */}
              {thread.reason && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                      Reason:
                    </span>
                    <span className="text-sm text-yellow-700 dark:text-yellow-300">
                      {thread.reason}
                    </span>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-500">
                  Thread ID: {thread.id}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(`https://twitter.com/i/status/${thread.root_tweet_id}`, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View Tweet
                  </Button>
                  {thread.status === "pending" && (
                    <Button size="sm">
                      Force Process
                    </Button>
                  )}
                  {thread.status === "failed" && (
                    <Button size="sm">
                      Retry
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredThreads.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No threads found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {filterStatus !== "all"
                  ? `No threads with status "${filterStatus}".`
                  : "No conversation threads available."}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}