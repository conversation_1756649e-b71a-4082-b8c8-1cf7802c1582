"use client";

import Link from "next/link";
import { useSafeAuth } from "@/hooks/use-safe-auth";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Bot, 
  Zap, 
  Brain, 
  Shield, 
  MessageSquare, 
  ArrowRight, 
  CheckCircle,
  Twitter
} from "lucide-react";

export default function Home() {
  const { isSignedIn, isMounted } = useSafeAuth();

  const features = [
    {
      icon: Bot,
      title: "AI-Powered Responses",
      description: "Intelligent Twitter bot that responds to mentions with context-aware replies"
    },
    {
      icon: Brain,
      title: "Smart Classification",
      description: "Automatically classify mentions and decide when to respond or skip"
    },
    {
      icon: Shield,
      title: "Content Moderation",
      description: "Built-in policy rules to block unwanted users and filter content"
    },
    {
      icon: Zap,
      title: "Real-time Processing",
      description: "Process mentions instantly with webhook support and live monitoring"
    }
  ];

  const benefits = [
    "Automated customer support on Twitter",
    "Intelligent conversation management", 
    "Customizable AI personality and responses",
    "Advanced analytics and monitoring",
    "Easy setup with multiple AI providers",
    "Secure API key management"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-16 max-w-6xl">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex justify-center items-center mb-6">
            <div className="bg-blue-600 p-3 rounded-2xl mr-4">
              <Bot className="h-12 w-12 text-white" />
            </div>
            <div className="flex items-center">
              <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                AskBuddy
              </h1>
              <Twitter className="h-8 w-8 text-blue-500 ml-3" />
            </div>
          </div>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            The intelligent Twitter bot that automatically responds to mentions with AI-powered, 
            context-aware replies. Perfect for customer support, engagement, and community management.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {isMounted && isSignedIn ? (
              <Link href="/admin">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                  Go to Dashboard
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            ) : (
              <>
                <Link href="/sign-up">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                    Get Started Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/sign-in">
                  <Button variant="outline" size="lg" className="px-8 py-3">
                    Sign In
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow">
                <CardHeader className="text-center pb-4">
                  <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full w-fit mx-auto mb-4">
                    <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Benefits Section */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Why Choose AskBuddy?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Transform your Twitter presence with intelligent automation that feels human
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-4 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Automate Your Twitter Responses?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Set up your AI-powered Twitter bot in minutes, not hours
          </p>
          
          {!isMounted || !isSignedIn ? (
            <Link href="/sign-up">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3">
                Start Your Free Account
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          ) : null}
        </div>

        {/* Footer */}
        <div className="text-center mt-16 pt-8 border-t border-gray-200 dark:border-gray-700">
          <p className="text-gray-600 dark:text-gray-400">
            Built with AI technology to help you engage better on Twitter
          </p>
        </div>
      </div>
    </div>
  );
}