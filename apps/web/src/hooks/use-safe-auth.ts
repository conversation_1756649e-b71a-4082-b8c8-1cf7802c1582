"use client";

import { useState, useEffect } from "react";

export function useSafeAuth() {
  const [mounted, setMounted] = useState(false);
  const [authState, setAuthState] = useState({
    isSignedIn: false,
    isLoaded: false
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      try {
        // Try to import and use Clerk
        const { useAuth } = require("@clerk/nextjs");
        const auth = useAuth();
        setAuthState({
          isSignedIn: auth.isSignedIn || false,
          isLoaded: auth.isLoaded || false
        });
      } catch (error) {
        // Clerk not available or configured, fallback to signed out state
        console.warn("Clerk not configured, running in development mode");
        setAuthState({
          isSignedIn: false,
          isLoaded: true
        });
      }
    }
  }, [mounted]);

  return {
    ...authState,
    isMounted: mounted
  };
}