"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  Bot,
  MessageSquare,
  Settings,
  BarChart3,
  Zap,
  Key,
  Shield,
  Brain,
  Plus,
} from "lucide-react";

const navigation = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: BarChart3,
  },
  {
    name: "Setup",
    href: "/admin/setup",
    icon: Plus,
  },
  {
    name: "Bot Settings",
    href: "/admin/bot",
    icon: Bot,
  },
  {
    name: "AI Agent",
    href: "/admin/ai",
    icon: Brain,
  },
  {
    name: "API Keys",
    href: "/admin/keys",
    icon: Key,
  },
  {
    name: "Policy Rules",
    href: "/admin/policy",
    icon: Shield,
  },
  {
    name: "Mentions",
    href: "/admin/mentions",
    icon: MessageSquare,
  },
  {
    name: "Threads",
    href: "/admin/threads",
    icon: Zap,
  },
];

export function Sidebar() {
  const pathname = usePathname();

  return (
    <div className="w-64 bg-white dark:bg-gray-800 shadow-sm">
      <div className="flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Bot className="h-8 w-8 text-blue-600" />
          <span className="ml-2 text-lg font-semibold text-gray-900 dark:text-white">
            AskBuddy
          </span>
        </div>
      </div>
      
      <nav className="mt-8">
        <div className="px-3">
          {navigation.map((item) => {
            const isActive = pathname === item.href || 
              (item.href !== "/admin" && pathname.startsWith(item.href));
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center px-3 py-2 text-sm font-medium rounded-md mb-1",
                  isActive
                    ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                    : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                )}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            );
          })}
        </div>
      </nav>

      <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div className="flex items-center">
            <div className="h-3 w-3 bg-green-400 rounded-full mr-2"></div>
            <span className="text-sm text-gray-600 dark:text-gray-300">
              System Online
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}