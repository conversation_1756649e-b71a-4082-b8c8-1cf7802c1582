"use client";
import Link from "next/link";
import { User<PERSON><PERSON>on } from "@clerk/nextjs";
import { But<PERSON> } from "./ui/button";
import { <PERSON><PERSON> } from "lucide-react";
import { ModeToggle } from "./mode-toggle";
import { useSafeAuth } from "@/hooks/use-safe-auth";

export default function Header() {
  const { isSignedIn, isMounted } = useSafeAuth();

  return (
    <div>
      <div className="flex flex-row items-center justify-between px-4 py-3">
        <div className="flex items-center">
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <Bot className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              AskBuddy
            </span>
          </Link>
        </div>
        
        <nav className="flex items-center gap-4">
          {isMounted && isSignedIn ? (
            <>
              <Link href="/admin">
                <Button variant="ghost">
                  Dashboard
                </Button>
              </Link>
              <ModeToggle />
              <UserButton
                appearance={{
                  elements: {
                    avatarBox: "w-8 h-8"
                  }
                }}
                afterSignOutUrl="/"
                userProfileMode="navigation"
                userProfileUrl="/account"
              />
            </>
          ) : (
            <>
              <Link href="/sign-in">
                <Button variant="ghost">
                  Sign In
                </Button>
              </Link>
              <Link href="/sign-up">
                <Button>
                  Get Started
                </Button>
              </Link>
              <ModeToggle />
            </>
          )}
        </nav>
      </div>
      <hr className="border-gray-200 dark:border-gray-700" />
    </div>
  );
}
