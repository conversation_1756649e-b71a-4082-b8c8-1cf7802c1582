"use client";

import { useAuth } from "@clerk/nextjs";
import { useEffect } from "react";

export function ClerkTokenDebug() {
  const { getToken, isSignedIn } = useAuth();

  useEffect(() => {
    if (!isSignedIn) {
      console.log("[ClerkTokenDebug] User not signed in");
      return;
    }

    (async () => {
      try {
        console.group("[ClerkTokenDebug] Token Analysis");
        
        // Get the convex token
        const token = await getToken({ template: "convex" });
        console.log("[ClerkTokenDebug] Token received:", !!token);
        
        if (token) {
          console.log("[ClerkTokenDebug] Token preview:", token.slice(0, 50) + "...");
          
          // Decode JWT payload (just for debugging - don't do this in production)
          try {
            const parts = token.split('.');
            if (parts.length === 3) {
              const payload = JSON.parse(atob(parts[1]));
              console.log("[ClerkTokenDebug] JWT Payload:", {
                iss: payload.iss,
                aud: payload.aud,
                sub: payload.sub,
                exp: payload.exp,
                iat: payload.iat,
                // Show all claims for debugging
                allClaims: payload
              });
              
              // Check if issuer matches expected
              const expectedIssuer = "https://in-turtle-40.clerk.accounts.dev";
              const expectedAud = "convex";
              
              console.log("[ClerkTokenDebug] Validation Check:", {
                issuerMatch: payload.iss === expectedIssuer,
                expectedIssuer,
                actualIssuer: payload.iss,
                audMatch: payload.aud === expectedAud,
                expectedAud,
                actualAud: payload.aud
              });
            }
          } catch (decodeError) {
            console.error("[ClerkTokenDebug] Failed to decode JWT:", decodeError);
          }
        } else {
          console.warn("[ClerkTokenDebug] No token received - check JWT template exists");
        }
      } catch (error) {
        console.error("[ClerkTokenDebug] Error getting token:", error);
      } finally {
        console.groupEnd();
      }
    })();
  }, [getToken, isSignedIn]);

  return null; // This is a debug component, no UI
}
