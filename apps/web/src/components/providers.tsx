"use client";

import { ConvexProviderWithClerk } from "convex/react-clerk";
import { ConvexProvider, ConvexReactClient } from "convex/react";
import { ClerkProvider, useAuth } from "@clerk/nextjs";
import { ThemeProvider } from "./theme-provider";
import { Toaster } from "./ui/sonner";
import { useState, useEffect } from "react";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export default function Providers({
  children
}: {
  children: React.ReactNode
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const pk = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

  // Robust logging to help diagnose auth provider issues
  try {
    console.groupCollapsed("[Providers] Init");
    console.log("[Providers] Clerk PK present:", <PERSON><PERSON><PERSON>(pk));
    if (!pk) {
      console.warn(
        "[Providers] NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is missing. <PERSON><PERSON><PERSON><PERSON> will be disabled. Sign-in routes that rely on Clerk will break."
      );
    } else if (!pk.startsWith("pk_")) {
      console.warn(
        "[Providers] NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY does not start with 'pk_'. Check your .env.local configuration."
      );
    }
  } catch (err) {
    console.error("[Providers] Logging error:", err);
  } finally {
    console.groupEnd?.();
  }

  // IMPORTANT: Do NOT gate ClerkProvider on `mounted`.
  // Gating caused SignIn/SignUp to render outside ClerkProvider on first paint.
  const enableClerk = !!pk && pk.startsWith("pk_");

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {enableClerk ? (
        <ClerkProvider publishableKey={pk}>
          <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
            {children}
          </ConvexProviderWithClerk>
        </ClerkProvider>
      ) : (
        <ConvexProvider client={convex}>{children}</ConvexProvider>
      )}
      <Toaster richColors />
    </ThemeProvider>
  );
}
