"use client";

import { ConvexProviderWithClerk } from "convex/react-clerk";
import { ConvexProvider, ConvexReactClient } from "convex/react";
import { ClerkProvider, useAuth } from "@clerk/nextjs";
import { ThemeProvider } from "./theme-provider";
import { Toaster } from "./ui/sonner";
import { useState, useEffect } from "react";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export default function Providers({
  children
}: {
  children: React.ReactNode
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const pk = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
  const enableClerk = mounted && !!pk && pk.startsWith("pk_");

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {enableClerk ? (
        <ClerkProvider publishableKey={pk}>
          <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
            {children}
          </ConvexProviderWithClerk>
        </ClerkProvider>
      ) : (
        <ConvexProvider client={convex}>{children}</ConvexProvider>
      )}
      <Toaster richColors />
    </ThemeProvider>
  );
}
