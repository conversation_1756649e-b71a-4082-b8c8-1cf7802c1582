# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Setup and development
bun install                    # Install dependencies
bun dev:setup                 # Setup Convex backend (run once)
bun dev                       # Start all applications
bun dev:web                   # Start only web app (port 3001)
bun dev:server                # Start only Convex backend

# Building and testing  
bun build                     # Build all applications
bun check-types              # TypeScript type checking
turbo lint                   # Run linting across all packages

# PWA assets (from web app directory)
cd apps/web && bun generate-pwa-assets
```

## Architecture Overview

This is a **Turborepo monorepo** using **Convex** as the reactive backend-as-a-service platform with **Next.js** as the frontend framework.

### Key Components:

- **apps/web/**: Next.js frontend application (runs on port 3001)
- **apps/fumadocs/**: Documentation site using Fumadocs (runs on port 4000)  
- **packages/backend/**: Convex backend functions and database schema

### Technology Stack:

- **Package Manager**: Bun
- **Monorepo Tool**: Turborepo
- **Frontend**: Next.js 15 with React 19, TailwindCSS 4, shadcn/ui
- **Backend**: Convex (reactive backend-as-a-service)
- **Forms**: @tanstack/react-form with Zod validation
- **UI**: Radix UI primitives, Lucide icons, next-themes
- **PWA**: Progressive Web App capabilities included

### Database & Backend:

The Convex backend in `packages/backend/convex/` contains:
- `schema.ts`: Database schema definitions
- `todos.ts`: Example CRUD operations for todos
- Functions are reactive and real-time by default

### Development Notes:

- Uses workspace dependencies (`@AskBuddy/backend`: `workspace:*`)
- Turbopack enabled for faster development builds
- PWA manifest and service worker support included
- All packages use TypeScript with strict type checking

### First-Time Setup:

1. Run `bun install` to install dependencies
2. Run `bun dev:setup` to configure Convex project (follow prompts)
3. Run `bun dev` to start both frontend and backend
4. Access web app at http://localhost:3001

# MUST DO

Use docs/convex_rules.txt as a reference for Convex best practices.
