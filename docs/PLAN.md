# Convex Fix Plan

## Goal
Resolve Convex deployment errors caused by Node action modules exporting mutations/queries. Ensure `bunx convex dev --once` completes successfully and add observability.

## Checklist
- [x] Triage error logs from Convex CLI (400 InvalidModules: Mutation exported from Node module)
- [x] Locate offending function (`saveWebhookRule`) in `packages/backend/convex/twitter.ts` under a "use node" file
- [x] Move mutation into a non-node module (`packages/backend/convex/twitter_mutations.ts`)
- [x] Update call sites to reference `internal.twitter_mutations.saveWebhookRule`
- [x] Add robust console logging around mutation invocation and DB insert
- [x] Run `bunx convex dev --once` to validate schema/codegen and deployment
- [x] Quick audit: other "use node" files (`agents.ts`, `tools.ts`, `twitter.ts`) export only actions

## Outcome
- Convex dev push completes without errors.
- Function graph now adheres to Convex constraints: Node modules contain only actions; DB mutations live in standard modules.

## Files Touched / Added
- Added: `packages/backend/convex/twitter_mutations.ts`
  - Contains `internalMutation saveWebhookRule` with logging.
- Modified: `packages/backend/convex/twitter.ts`
  - Removed `internalMutation saveWebhookRule`.
  - Updated `setupWebhookRule` to call `internal.twitter_mutations.saveWebhookRule` and added logging.

## Next Steps
- If additional Convex errors appear, repeat triage and ensure no query/mutation is exported from any `"use node"` module.
- Consider adding unit tests for webhook rules and mention ingestion under a `tests/` directory.

