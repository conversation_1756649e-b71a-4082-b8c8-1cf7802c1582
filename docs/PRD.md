Below is a crisp PRD and a production‑ready Convex schema for **AskBuddy**, a Twitter (X) mention‑reply bot built on your specified stack.

---

## 1) PRD — AskBuddy

### 1.1 Overview

AskBuddy is a Twitter bot that replies whenever it’s **@mentioned** in a post or reply. It uses:

* **Better‑T Stack** for project scaffolding (Next.js frontend, Convex backend, Turborepo, PWA, Fumadocs). ([Better-T Stack][1])
* **Vercel AI SDK v5** for agents, streaming, and tool calling, with **AI Elements** for UI components where needed in the admin console. ([v5.ai-sdk.dev][2], [AI SDK][3], [Vercel][4])
* **OpenRouter** as the model provider, defaulting to **openai/gpt‑5‑mini** (primary), with streaming and structured output support. ([OpenRouter][5])
* **Firecrawl** for robust page scraping/crawling to markdown/HTML/JSON. ([docs.firecrawl.dev][6])
* **Exa** for high‑quality web search + content retrieval. ([docs.exa.ai][7])
* **xAI Live Search** (with X search source) to search **Twitter content** directly; `safe_search` is supported for web/news sources. ([xAI Docs][8])
* **docs.twitterapi.io** for mention polling/filters and posting replies via webhook rules or polling (third‑party X API). ([docs.twitterapi.io][9])

> ⚠️ Note: docs.twitterapi.io is a third‑party API. Confirm legal/compliance posture against X Developer Terms before production. If you later prefer official endpoints, the standard **user mentions** timeline exists in X/Twitter API v2; Postman’s public collections show the v2 surface. ([Postman][10])

---

### 1.2 Goals

* **Always reply** when @AskBuddy is mentioned (direct @mention or a reply tagging the bot).
* Cite sources when the answer leverages Firecrawl/Exa/xAI Live Search.
* Provide **low‑latency** responses (<5s p50) and **high reliability** with retries and idempotency.
* Admin console for: model settings, rules, rate limits, blacklists, prompt persona, tool toggles.

### 1.3 Non‑Goals

* DM automation beyond basic templated hand‑off.
* Long‑term analytics dashboarding beyond daily/weekly aggregates initially.

### 1.4 User Stories

* As a user on X, when I tag @AskBuddy with a question or link, I get a helpful reply with sources.
* As an admin, I can turn on/off Firecrawl, Exa, xAI Live Search per query class.
* As an admin, I can throttle/restrict replies to specific users/keywords.

### 1.5 Functional Requirements

**Triggers**

* Ingestion of events via either:

  1. **Webhook/Websocket filter rules** from docs.twitterapi.io for `@AskBuddy` mentions; or
  2. **Polling mentions** endpoint every N seconds with since cursor. ([docs.twitterapi.io][11])

**Routing Logic**

1. **Classification (small model pass)**

   * Determine intent: “answer general,” “summarize URL,” “fact‑check,” “Twitter‑only search,” etc.
   * If the tweet includes URLs → Firecrawl scrape (single) or crawl (if thread/source site). ([docs.firecrawl.dev][6])
   * If the question requires **web context** → Exa search + contents. ([docs.exa.ai][7])
   * If the question requires **Twitter‑only** context → xAI Live Search with `sources: [{ type: "x" }]` (and `safe_search` control for web/news if also used). ([xAI Docs][8])

2. **Answer generation (primary model via OpenRouter)**

   * **Model**: `openai/gpt-5-mini` (fast, cost‑effective). Fallbacks: `openrouter/auto` or a curated list (e.g., `openai/gpt-5-chat` for harder queries). Use streaming. ([OpenRouter][5])

3. **Posting**

   * Reply in‑thread to the mention using docs.twitterapi.io create tweet (reply) API. ([docs.twitterapi.io][12])

4. **Deduplication**

   * Idempotency key per mention (`tweet_id`) to prevent double replies on retries.

**Citations**

* When Firecrawl/Exa/xAI provides context, append short source list (max 3–5 links).
* When xAI Live Search is used, preserve citation URLs (API supports returning citations). ([xAI Docs][8])

**Moderation / Safety**

* Profanity/hate/NSFW filter (server‑side guardrails), throttle unknown accounts, and allow admin blocklists.
* Option to force `safe_search: true` for web/news in xAI Live Search; by default it’s on. ([xAI Docs][8])

**Rate Limits / Backoff**

* Respect OpenRouter and third‑party limits; check OpenRouter rate/credits endpoint for health and backoff. ([OpenRouter][13])

**Observability**

* Log tool calls, token usage, latency, and failures. Use daily aggregates.

### 1.6 AISDK v5 Best Practices to Follow

* **Tool calling with schemas** (Zod/JSON Schema) for Firecrawl/Exa/xAI search tools; validate inputs, then automatically pass tool results back via **multi‑step** `streamText` with `stopWhen`. ([AI SDK][3])
* **Agents** pattern: small classifier → tools → main model; keep routing simple, add complexity only if needed. ([AI SDK][14])
* **Streaming** responses to UI (admin console) using AI SDK v5 streaming helpers. ([v5.ai-sdk.dev][2])
* **AI Elements** for UI building blocks (message threads, reasoning panels) in the admin console. ([Vercel][4])
* **Provider management**: Point AI SDK to OpenRouter; enable structured outputs for deterministic citations block. ([v5.ai-sdk.dev][2], [OpenRouter][15])

### 1.7 System Architecture (high‑level)

* **Ingestion**:

  * Webhook or poller action receives mention → writes **mention\_event** doc (Convex).
* **Job queue** (Convex scheduled functions):

  * Enqueue `reply_job` with mention payload; retry with exponential backoff; idempotent by `tweet_id`. ([Convex Developer Hub][16], [Stack by Convex][17])
* **Agent service** (Convex action):

  * Classify → call tools (Firecrawl / Exa / xAI Live Search) → call OpenRouter model → assemble reply + citations.
* **Posting**:

  * Docs.twitterapi.io **create\_tweet\_v2**; store posted `reply_tweet_id`. ([docs.twitterapi.io][12])
* **Admin UI** (Next.js + AI Elements):

  * Streams run logs, tweak settings, blacklists, and prompt versions.

### 1.8 Admin Console Features

* Toggle tools (Firecrawl, Exa, xAI Live Search), set result caps, and `safe_search` flags. ([xAI Docs][8])
* Model routing and fallbacks (OpenRouter). ([OpenRouter][18])
* Throttling rules: per‑user cooldown, time windows, max replies/day.
* Prompt versioning & A/B.

### 1.9 Performance & SLOs

* **p50 < 5s**, **p95 < 15s** end‑to‑end for replies without crawling deep (network‑dependent).
* Idempotent processing with durable retries (Convex scheduler + action retrier patterns). ([Stack by Convex][19])

### 1.10 Security & Compliance

* Store third‑party API keys in environment secrets (Vercel/Convex environment), not in DB; DB stores only masked suffixes.
* Explicit notice: Using third‑party X APIs (docs.twitterapi.io) may have policy implications; prefer official X APIs where feasible. ([Postman][10])

### 1.11 Milestones

1. **MVP (Week 1–2)**: Mention ingestion + basic reply, Exa integration, OpenRouter GPT‑5 Mini, admin toggles.
2. **Web context (Week 3)**: Firecrawl integration, citations, safety filters.
3. **Twitter search (Week 4)**: xAI Live Search with `sources: [{ type: "x" }]`. ([xAI Docs][8])
4. **Hardening (Week 5)**: Rate limits, retries, admin insights.

---

## 2) Convex — Full Database Schema

> Notes:
>
> * Keep secrets in env; only store masked key metadata in DB (last 4 chars, createdAt).
> * Use indexes for hot paths (by `tweet_id`, `status`, timestamps).
> * Include search indexes for full‑text lookups on tweet/message text. ([Convex Developer Hub][20])
> * Use Convex **scheduled functions** for durable retries and cron cleanup. ([Convex Developer Hub][21])

Create `convex/schema.ts`:

```ts
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export const providerKind = v.union(
  v.literal("openrouter"),
  v.literal("exa"),
  v.literal("firecrawl"),
  v.literal("xai_live_search"),
  v.literal("twitterapi_io")
);

export const jobStatus = v.union(
  v.literal("queued"),
  v.literal("running"),
  v.literal("succeeded"),
  v.literal("failed"),
  v.literal("cancelled")
);

export const replyDecision = v.union(
  v.literal("auto_reply"),
  v.literal("skip"),
  v.literal("manual_review")
);

// Multi-tenant in case you run multiple bots in one install
export default defineSchema({
  // --- Core domain ---
  workspaces: defineTable({
    name: v.string(),
    ownerUserId: v.string(), // app admin auth user id
    settings: v.object({
      timezone: v.optional(v.string()),
    }),
  }).index("by_owner", ["ownerUserId"]),

  bot_accounts: defineTable({
    workspaceId: v.id("workspaces"),
    x_user_id: v.string(),       // the bot's X user id
    username: v.string(),        // @AskBuddy
    display_name: v.optional(v.string()),
    avatar_url: v.optional(v.string()),
    // whether to auto reply on mentions
    auto_reply_enabled: v.boolean(),
    // throttle limits
    per_minute_limit: v.number(),
    per_day_limit: v.number(),
    // masked token metadata only
    credentials: v.object({
      provider: v.string(), // e.g., "docs.twitterapi.io"
      key_label: v.optional(v.string()),
      key_last4: v.optional(v.string()),
    }),
  }).index("by_workspace", ["workspaceId"]),

  // Mention events ingested from webhook/poller
  mention_events: defineTable({
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
    tweet_id: v.string(),
    conversation_id: v.optional(v.string()),
    parent_tweet_id: v.optional(v.string()),
    author_user_id: v.string(),
    author_username: v.string(),
    text: v.string(),
    urls: v.array(v.string()),
    lang: v.optional(v.string()),
    created_at_ms: v.number(),
    processed: v.boolean(), // quick flag
    source: v.union(v.literal("webhook"), v.literal("poll")),
    raw: v.any(), // original normalized payload
  })
    .index("by_bot_tweet", ["botAccountId", "tweet_id"])
    .index("by_workspace_time", ["workspaceId", "created_at_ms"])
    .searchIndex("search_text", { searchField: "text" }),

  // One logical conversation per mention (thread)
  threads: defineTable({
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
    root_tweet_id: v.string(),
    last_mention_event_id: v.id("mention_events"),
    status: v.union(
      v.literal("pending"),
      v.literal("responded"),
      v.literal("skipped"),
      v.literal("failed")
    ),
    decision: replyDecision,
    reason: v.optional(v.string()),
    policy: v.object({
      allow_twitter_search: v.boolean(),
      allow_web_search: v.boolean(),
      allow_firecrawl: v.boolean(),
      safe_search_web_news: v.boolean(), // xAI param
    }),
    // model used for the final answer
    model: v.string(), // e.g., "openai/gpt-5-mini"
    created_at_ms: v.number(),
    updated_at_ms: v.number(),
  })
    .index("by_bot_status_time", ["botAccountId", "status", "updated_at_ms"])
    .index("by_workspace_time", ["workspaceId", "created_at_ms"]),

  // LLM messages (for auditability)
  messages: defineTable({
    threadId: v.id("threads"),
    role: v.union(v.literal("system"), v.literal("user"), v.literal("assistant"), v.literal("tool")),
    content: v.string(), // stored as markdown or plain text
    tool_name: v.optional(v.string()),
    tool_args: v.optional(v.any()),
    tool_result: v.optional(v.any()),
    model: v.optional(v.string()),
    token_usage: v.optional(v.object({
      input: v.optional(v.number()),
      output: v.optional(v.number()),
    })),
    created_at_ms: v.number(),
  })
    .index("by_thread_time", ["threadId", "created_at_ms"])
    .searchIndex("search_content", { searchField: "content" }),

  // Final replies posted on X
  replies: defineTable({
    threadId: v.id("threads"),
    reply_tweet_id: v.optional(v.string()),
    parent_tweet_id: v.string(),
    text: v.string(),
    status: v.union(
      v.literal("draft"),
      v.literal("posted"),
      v.literal("failed")
    ),
    error: v.optional(v.string()),
    posted_at_ms: v.optional(v.number()),
  }).index("by_thread_status", ["threadId", "status"]),

  // Jobs for durable async processing
  jobs: defineTable({
    type: v.union(
      v.literal("ingest_mention"),
      v.literal("classify"),
      v.literal("compose_answer"),
      v.literal("post_reply"),
      v.literal("backfill_mentions")
    ),
    workspaceId: v.id("workspaces"),
    payload: v.any(),
    status: jobStatus,
    attempt: v.number(),
    scheduled_for_ms: v.number(),
    started_at_ms: v.optional(v.number()),
    finished_at_ms: v.optional(v.number()),
    idempotency_key: v.optional(v.string()),
    error: v.optional(v.string()),
  })
    .index("by_workspace_status_time", ["workspaceId", "status", "scheduled_for_ms"])
    .index("by_type_status", ["type", "status"])
    .index("by_idempotency", ["idempotency_key"]),

  // Tool caches to reduce latency/cost
  exa_cache: defineTable({
    workspaceId: v.id("workspaces"),
    query_hash: v.string(),
    query: v.string(),
    results: v.any(),
    created_at_ms: v.number(),
    ttl_ms: v.number(),
  }).index("by_workspace_qhash", ["workspaceId", "query_hash"]),

  firecrawl_cache: defineTable({
    workspaceId: v.id("workspaces"),
    url_hash: v.string(),
    url: v.string(),
    formats: v.array(v.string()), // ["markdown","html","json"]
    data: v.any(), // markdown/html/json payload
    created_at_ms: v.number(),
    ttl_ms: v.number(),
  }).index("by_workspace_urlhash", ["workspaceId", "url_hash"]),

  twitter_cache: defineTable({
    workspaceId: v.id("workspaces"),
    tweet_id: v.string(),
    data: v.any(),
    fetched_at_ms: v.number(),
  }).index("by_workspace_tweet", ["workspaceId", "tweet_id"]),

  // Settings & prompts
  agent_profiles: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(), // "AskBuddy default"
    system_prompt: v.string(),
    temperature: v.number(),
    top_p: v.optional(v.number()),
    model_primary: v.string(), // "openai/gpt-5-mini"
    model_fallbacks: v.array(v.string()),
    tool_defaults: v.object({
      firecrawl_enabled: v.boolean(),
      exa_enabled: v.boolean(),
      xai_live_search_enabled: v.boolean(),
      xai_search_max_results: v.number(),
      safe_search_web_news: v.boolean(),
    }),
    updated_at_ms: v.number(),
  }).index("by_workspace_name", ["workspaceId", "name"]),

  // API key metadata (masked)
  provider_keys: defineTable({
    workspaceId: v.id("workspaces"),
    provider: providerKind,
    label: v.string(),
    last4: v.optional(v.string()),
    created_at_ms: v.number(),
    enabled: v.boolean(),
  }).index("by_workspace_provider", ["workspaceId", "provider"]),

  // Moderation & policy
  moderation_flags: defineTable({
    threadId: v.id("threads"),
    category: v.string(), // "toxicity", "nsfw", etc.
    score: v.number(),    // 0..1
    notes: v.optional(v.string()),
    created_at_ms: v.number(),
  }).index("by_thread_category", ["threadId", "category"]),

  // Analytics (rollups)
  daily_metrics: defineTable({
    workspaceId: v.id("workspaces"),
    date_utc: v.string(), // "2025-08-13"
    mentions: v.number(),
    replies_posted: v.number(),
    avg_latency_ms: v.number(),
    token_in: v.number(),
    token_out: v.number(),
    costs_usd_est: v.number(),
  }).index("by_workspace_day", ["workspaceId", "date_utc"]),

  // Blacklists / allowlists
  policy_rules: defineTable({
    workspaceId: v.id("workspaces"),
    ruleType: v.union(
      v.literal("block_user"),
      v.literal("block_keyword"),
      v.literal("allow_user")
    ),
    value: v.string(), // username, user_id, or keyword
    notes: v.optional(v.string()),
    created_at_ms: v.number(),
  }).index("by_workspace_type_value", ["workspaceId", "ruleType", "value"]),

  // Webhook config (for docs.twitterapi.io)
  webhook_rules: defineTable({
    workspaceId: v.id("workspaces"),
    rule_id: v.optional(v.string()),
    tag: v.string(),   // e.g. "mentions"
    value: v.string(), // query string to match @AskBuddy
    active: v.boolean(),
    interval_seconds: v.number(),
    created_at_ms: v.number(),
    updated_at_ms: v.number(),
  }).index("by_workspace_active", ["workspaceId", "active"]),
});
```

**Why these tables?**

* `mention_events`, `threads`, `messages`, `replies` give you an auditable conversation graph.
* `jobs` enables **durable** processing with retries and idempotency in Convex scheduled functions. ([Convex Developer Hub][21])
* `exa_cache`, `firecrawl_cache`, `twitter_cache` reduce repeated external calls (latency and cost). ([docs.exa.ai][7], [docs.firecrawl.dev][6])
* `agent_profiles` centralizes AISDK v5 behavior (model + tool defaults). ([v5.ai-sdk.dev][2])
* `webhook_rules` matches docs.twitterapi.io’s filter‑rule workflow. ([docs.twitterapi.io][11])
* Search indexes on text fields support quick investigation and admin search. ([Convex Developer Hub][20])

---

## 3) Integration Notes (brief)

**Scaffold**

* `bun create better-t-stack@latest . --yes --frontend next --backend convex --addons fumadocs pwa turborepo` (per Better‑T Stack). After scaffold, add AI SDK v5 and AI Elements in the admin UI. ([Better-T Stack][1], [Vercel][4])

**AISDK v5**

* Use `streamText` + **tools** for Firecrawl/Exa/xAI. Prefer **Zod** schemas; let the SDK handle **multi‑step** tool execution; keep agent logic simple. ([AI SDK][3])

**OpenRouter**

* Default model: `openai/gpt-5-mini` with streaming. Consider `openrouter/auto` as fallback for resilience and cost‑aware routing. Watch your key/limits via `/api/v1/key`. ([OpenRouter][5])

**Firecrawl**

* For any URL in the mention: `scrape` → markdown + metadata; for multi‑page sources: `crawl` job; cache by URL hash. ([docs.firecrawl.dev][6])

**Exa**

* General web answers: `POST /search` with `text: true`; optionally `Get contents` for summaries. Cache by query hash. ([docs.exa.ai][7])

**xAI Live Search**

* Twitter‑only queries: `search_parameters: { mode: "on", sources: [{ type: "x" }] }`.
* For web/news, you can tune `safe_search` and `country`; keep `return_citations` enabled (default). ([xAI Docs][8])

**docs.twitterapi.io**

* Mentions: `GET /twitter/user/mentions` with cursor for polling.
* Webhooks/filters: `POST /oapi/tweet_filter/add_rule` then activate; store rule id.
* Reply: `POST /twitter/create_tweet_v2` with `reply_to_tweet_id`. (Requires their cookie‑based login flow.) ([docs.twitterapi.io][9])

**Convex**

* Use **scheduled functions** for retries/backoff; consider daily cron to backfill mentions and compute `daily_metrics`. ([Convex Developer Hub][21])

---

## 4) Risks & Mitigations

* **API/Policy risk (X)**: 3rd‑party Twitter APIs may violate X terms; keep an abstraction to swap to official endpoints if needed. ([Postman][10])
* **Latency**: Heavy crawls/searches may breach SLOs → do classification first; only fetch what’s necessary; cache aggressively. ([docs.firecrawl.dev][6], [docs.exa.ai][7])
* **Costs**: Track token usage and searches in `daily_metrics`; cap max search results (xAI `max_search_results`). ([xAI Docs][8])
* **Model availability**: Use OpenRouter fallbacks & routing. ([OpenRouter][18])

---

## 5) Success Metrics

* Reply coverage (% of mentions answered)
* Median/95th latency from mention to post
* Source usage mix (Firecrawl/Exa/xAI) and cost per reply
* Human feedback score (admin‑rated threads)
* Rate‑limit & error rates

---

If you want, I can follow up with:

* **convex functions stubs** for `ingestMention`, `classify`, `composeAnswer`, `postReply`, and scheduled retries.
* An **AISDK v5 tool layer** (Zod) for Firecrawl/Exa/xAI Live Search, wired to OpenRouter `gpt-5-mini`.

[1]: https://better-t-stack.dev/?utm_source=chatgpt.com "Better-T Stack"
[2]: https://v5.ai-sdk.dev/docs/introduction "AI SDK by Vercel"
[3]: https://ai-sdk.dev/docs/foundations/tools "Foundations: Tools"
[4]: https://vercel.com/changelog/introducing-ai-elements "Introducing AI Elements: Prebuilt, composable AI SDK components - Vercel"
[5]: https://openrouter.ai/openai/gpt-5-mini?utm_source=chatgpt.com "GPT-5 Mini - API, Providers, Stats | OpenRouter"
[6]: https://docs.firecrawl.dev/introduction "Quickstart | Firecrawl"
[7]: https://docs.exa.ai/reference/search "Search - Exa"
[8]: https://docs.x.ai/docs/guides/live-search "Live Search - Guides | xAI Docs"
[9]: https://docs.twitterapi.io/api-reference/endpoint/get_user_mention "twitterapi.io - Twitter data, 96% cheaper. No auth, no limits, just API."
[10]: https://www.postman.com/postman-student-programs/twitter-learning-modules-in-postman/collection/fsstmkm/twitter-api-v2?utm_source=chatgpt.com "Twitter API v2 | Get Started | Postman API Network"
[11]: https://docs.twitterapi.io/api-reference/endpoint/add_webhook_rule "twitterapi.io - Twitter data, 96% cheaper. No auth, no limits, just API."
[12]: https://docs.twitterapi.io/api-reference/endpoint/create_tweet_v2 "twitterapi.io - Twitter data, 96% cheaper. No auth, no limits, just API."
[13]: https://openrouter.ai/docs/api-reference/limits?utm_source=chatgpt.com "API Rate Limits | Configure Usage Limits in OpenRouter | OpenRouter ..."
[14]: https://ai-sdk.dev/docs/foundations/agents "Foundations: Agents"
[15]: https://openrouter.ai/docs/features/structured-outputs?utm_source=chatgpt.com "Structured Outputs | Enforce JSON Schema in OpenRouter API Responses ..."
[16]: https://docs.convex.dev/scheduling?utm_source=chatgpt.com "Scheduling | Convex Developer Hub"
[17]: https://stack.convex.dev/background-job-management?utm_source=chatgpt.com "Background Job Management - stack.convex.dev"
[18]: https://openrouter.ai/docs/features/model-routing?utm_source=chatgpt.com "Model Routing | Dynamic AI Model Selection and Fallback - OpenRouter"
[19]: https://stack.convex.dev/durable-workflows-and-strong-guarantees?utm_source=chatgpt.com "Agents Need Durable Workflows and Strong Guarantees"
[20]: https://docs.convex.dev/search/text-search?utm_source=chatgpt.com "Full Text Search | Convex Developer Hub"
[21]: https://docs.convex.dev/scheduling/scheduled-functions?utm_source=chatgpt.com "Scheduled Functions | Convex Developer Hub"

