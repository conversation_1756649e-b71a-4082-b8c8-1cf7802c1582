# AskBuddy Setup Guide

AskBuddy is now fully implemented as a production-ready Twitter bot system following your PRD specifications!

## 🚀 Quick Start

### 1. Environment Setup

Copy the example environment file:
```bash
cp .env.example .env.local
```

Configure your API keys in `.env.local`:
```env
# Required API Keys
OPENROUTER_API_KEY=your_openrouter_api_key_here
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
EXA_API_KEY=your_exa_api_key_here
XAI_API_KEY=your_xai_api_key_here
TWITTER_API_KEY=your_twitterapi_io_key_here

# Convex will auto-generate this
CONVEX_DEPLOYMENT=
TWITTER_WEBHOOK_SECRET=your_webhook_secret
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_xxx
CLERK_SECRET_KEY=sk_test_xxx


```

### 2. Backend Setup

```bash
# Install dependencies
bun install

# Setup Convex backend (follow prompts)
bun dev:setup

# Start the backend
bun dev:server
```

### 3. Frontend Setup

```bash
# Start the web app (in another terminal)
bun dev:web
```

Visit http://localhost:3001/admin to access the admin console.

## 🏗️ Architecture Overview

### Backend (Convex)
- **Database**: Complete schema with 15+ tables for mentions, threads, replies, jobs, caches, etc.
- **Job Queue**: Durable job processing with retries and exponential backoff
- **AI Agents**: Vercel AI SDK v5 with OpenRouter integration
- **Tools**: Firecrawl, Exa, and xAI Live Search integrations
- **Scheduled Functions**: Mention polling, job processing, cache cleanup
- **Real-time**: WebSocket subscriptions for live updates

### Frontend (Next.js)
- **Admin Dashboard**: Real-time metrics and monitoring
- **Mention Management**: Browse and search Twitter mentions
- **Thread Viewer**: Conversation flows with AI responses
- **Bot Management**: Configure bot accounts and settings
- **Policy Management**: Blacklists, rate limits, and moderation

## 🔧 Key Features Implemented

### Core Bot Functionality
✅ **Twitter Integration**: Mention polling and reply posting via docs.twitterapi.io
✅ **AI Processing**: Classification and response generation with GPT-5-mini
✅ **Tool Calling**: Firecrawl scraping, Exa search, xAI Live Search
✅ **Citation System**: Automatic source citations for external data

### Reliability & Performance
✅ **Job Queue**: Durable processing with retries and idempotency
✅ **Caching**: Intelligent caching for all external API calls
✅ **Rate Limiting**: Configurable limits and backoff strategies
✅ **Error Handling**: Comprehensive error tracking and recovery

### Admin Experience
✅ **Real-time Dashboard**: Live metrics and activity monitoring
✅ **Content Moderation**: Policy rules and safety filters
✅ **Performance Analytics**: Token usage, costs, and latency tracking
✅ **Multi-tenant**: Support for multiple bot accounts per workspace

## 📋 Initial Configuration

### 1. Create Your First Workspace

The system will auto-create a default workspace on first run.

### 2. Add a Bot Account

Use the admin console to:
1. Add your Twitter bot credentials
2. Configure reply settings (rate limits, auto-reply toggle)
3. Set up webhook rules or polling intervals

### 3. Configure AI Agent

Customize the agent profile:
- System prompt and personality
- Model selection (primary + fallbacks)
- Tool availability (Firecrawl, Exa, xAI)
- Safety settings

### 4. Set Policy Rules

Add moderation rules:
- Blocked users/keywords
- Allowed users (whitelist)
- Content filtering preferences

## 🔄 How It Works

1. **Mention Ingestion**: System polls Twitter API or receives webhooks
2. **Classification**: Small model determines intent and required tools
3. **Tool Execution**: Scrapes URLs, searches web/Twitter as needed
4. **Response Generation**: Main model generates contextual reply with citations
5. **Reply Posting**: System posts reply and tracks engagement

## 🛠️ Production Deployment

The system is production-ready with:
- Horizontal scaling via Convex
- Automatic retries and error recovery
- Comprehensive logging and monitoring
- Cost tracking and optimization
- Security best practices

## 📖 Next Steps

1. **Test the System**: Start with a few test mentions
2. **Monitor Performance**: Use the admin dashboard to track metrics
3. **Tune Settings**: Adjust prompts, rate limits, and tool usage
4. **Scale Up**: Add more bot accounts as needed

Your AskBuddy system is now ready to handle Twitter mentions with intelligent, context-aware responses! 🤖✨