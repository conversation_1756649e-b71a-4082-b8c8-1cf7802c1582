import { httpAction } from "./_generated/server";
import { httpRouter } from "convex/server";
import { api, internal } from "./_generated/api";
import { verifyWebhookSecret, normalizeTwitterPayload } from "./util/webhook";
import { logError, logInfo } from "./util/log";

export const http = httpRouter();

// POST /twitter/webhook
http.route({
  path: "/twitter/webhook",
  method: "POST",
  handler: httpAction(async (ctx, req) => {
    const startedAt = Date.now();
    const requestId = crypto.randomUUID?.() || Math.random().toString(36).slice(2);

    try {
      if (!verifyWebhookSecret(req.headers)) {
        return new Response(JSON.stringify({ ok: false, error: "unauthorized" }), { status: 401 });
      }

      // Parse body
      const contentType = req.headers.get("content-type") || "";
      let body: any = null;
      if (contentType.includes("application/json")) {
        body = await req.json();
      } else {
        const text = await req.text();
        try { body = JSON.parse(text); } catch { body = { raw: text }; }
      }

      logInfo("http.twitter.webhook_received", { requestId });

      const normalized = normalizeTwitterPayload(body);
      if (!normalized) {
        logError("http.twitter.normalize.fail", { requestId });
        return new Response(JSON.stringify({ ok: false, error: "bad_payload" }), { status: 400 });
      }

      // Resolve workspace and bot account. In a real deployment, tag or rule may include bot handle.
      // For now, try to find a bot account by username if present.
      // Try rule-based mapping first: expect body.rule.tag & body.rule.value
      const tag = (body?.rule?.tag || body?.tag || "").toString();
      const value = (body?.rule?.value || body?.value || "").toString();

      const workspaces = await ctx.runQuery(api.admin.getWorkspaces, {} as any);
      for (const ws of workspaces as any[]) {
        let matchedBot: any | null = null;
        if (tag && value) {
          const rule = await ctx.runQuery(internal.queries.getWebhookRuleByTagValue, { workspaceId: ws._id, tag, value });
          if (rule) {
            const handle = (value.startsWith("@") ? value.slice(1) : value).toLowerCase();
            const botAccounts = await ctx.runQuery(api.admin.getBotAccounts, { workspaceId: ws._id } as any);
            matchedBot = (botAccounts as any[]).find((b) => (b.username || "").toLowerCase() === handle);
          }
        }

        // Fallback heuristic if no rule match
        if (!matchedBot) {
          const botAccounts = await ctx.runQuery(api.admin.getBotAccounts, { workspaceId: ws._id } as any);
          matchedBot = (botAccounts as any[]).find((bot) => (normalized.text || "").toLowerCase().includes(`@${(bot.username || "").toLowerCase()}`));
        }

        if (matchedBot) {
          await ctx.runMutation(internal.mentions.createMention, {
            workspaceId: ws._id,
            botAccountId: matchedBot._id,
            tweetId: normalized.tweetId,
            conversationId: normalized.conversationId,
            parentTweetId: normalized.parentTweetId,
            authorUserId: normalized.authorUserId,
            authorUsername: normalized.authorUsername || "",
            text: normalized.text,
            urls: normalized.urls,
            lang: normalized.lang,
            source: "webhook" as const,
            raw: body,
          });

          logInfo("http.twitter.accepted", { requestId, workspaceId: ws._id, botAccountId: matchedBot._id, tag, value });
          return new Response(JSON.stringify({ ok: true }), { status: 202 });
        }
      }

      logError("http.twitter.no_match", { requestId, tag, value });
      return new Response(JSON.stringify({ ok: false, error: "no_matching_bot" }), { status: 202 });
    } catch (error) {
      console.error("[http.twitter] unhandled error", { requestId, error: error instanceof Error ? error.message : String(error) });
      return new Response(JSON.stringify({ ok: false, error: "server_error" }), { status: 500 });
    }
  })
});

export default http;

