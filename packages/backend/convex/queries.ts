import { v } from "convex/values";
import { internalQuery } from "./_generated/server";

// Get Exa cache entry
export const getExaCache = internalQuery({
  args: {
    workspaceId: v.id("workspaces"),
    queryHash: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, queryHash }) => {
    return await ctx.db
      .query("exa_cache")
      .withIndex("by_workspace_qhash", (q: any) =>
        q.eq("workspaceId", workspaceId).eq("query_hash", queryHash)
      )
      .first();
  },
});

// Query Exa cache (duplicate function for compatibility)
export const queryExaCache = internalQuery({
  args: {
    workspaceId: v.id("workspaces"),
    queryHash: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, queryHash }) => {
    return await ctx.db
      .query("exa_cache")
      .withIndex("by_workspace_qhash", (q) =>
        q.eq("workspaceId", workspaceId).eq("query_hash", queryHash)
      )
      .first();
  },
});

// Get Firecrawl cache entry
export const getFirecrawlCache = internalQuery({
  args: {
    workspaceId: v.id("workspaces"),
    urlHash: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, urlHash }) => {
    return await ctx.db
      .query("firecrawl_cache")
      .withIndex("by_workspace_urlhash", (q: any) =>
        q.eq("workspaceId", workspaceId).eq("url_hash", urlHash)
      )
      .first();
  },
});

// Query Firecrawl cache (duplicate function for compatibility)
export const queryFirecrawlCache = internalQuery({
  args: {
    workspaceId: v.id("workspaces"),
    urlHash: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, urlHash }) => {
    return await ctx.db
      .query("firecrawl_cache")
      .withIndex("by_workspace_urlhash", (q) =>
        q.eq("workspaceId", workspaceId).eq("url_hash", urlHash)
      )
      .first();
  },
});

// Get cached Twitter data
export const getTwitterCache = internalQuery({
  args: {
    workspaceId: v.id("workspaces"),
    tweetId: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, tweetId }) => {
    const cached = await ctx.db
      .query("twitter_cache")
      .withIndex("by_workspace_tweet", (q) =>
        q.eq("workspaceId", workspaceId).eq("tweet_id", tweetId)
      )
      .first();

    // Check if cache is still valid (30 minutes)
    if (cached && cached.fetched_at_ms > (Date.now() - 1800000)) {
      return cached.data;
    }

    return null;
  },
});

// Get webhook rules for workspace
export const getWebhookRules = internalQuery({
  args: {
    workspaceId: v.id("workspaces"),
    activeOnly: v.optional(v.boolean()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, activeOnly = true }) => {
    let query = ctx.db
      .query("webhook_rules")
      .withIndex("by_workspace_active", (q) => q.eq("workspaceId", workspaceId));

    if (activeOnly) {
      query = query.filter((q) => q.eq(q.field("active"), true));
    }

    return await query.collect();
  },
});

// Get webhook rule by tag/value in a workspace
export const getWebhookRuleByTagValue = internalQuery({
  args: {
    workspaceId: v.id("workspaces"),
    tag: v.string(),
    value: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, tag, value }) => {
    return await ctx.db
      .query("webhook_rules")
      .withIndex("by_workspace_tag_value", (q) => q.eq("workspaceId", workspaceId).eq("tag", tag).eq("value", value))
      .first();
  },
});

// Internal: list all workspaces (for server use)
export const listAllWorkspaces = internalQuery({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    return await ctx.db.query("workspaces").collect();
  },
});
