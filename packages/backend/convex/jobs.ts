import { v } from "convex/values";
import { internalAction, internalMutation, internalQuery } from "./_generated/server";
import { api, internal } from "./_generated/api";
import { logInfo, logError } from "./util/log";
import type { Id } from "./_generated/dataModel";

// Schedule a job for processing
export const scheduleJob = internalMutation({
  args: {
    type: v.union(
      v.literal("ingest_mention"),
      v.literal("classify"),
      v.literal("compose_answer"),
      v.literal("post_reply"),
      v.literal("backfill_mentions")
    ),
    workspaceId: v.id("workspaces"),
    payload: v.any(),
    idempotencyKey: v.optional(v.string()),
    delayMs: v.optional(v.number()),
  },
  returns: v.id("jobs"),
  handler: async (ctx, args) => {
    const now = Date.now();
    const scheduledFor = now + (args.delayMs || 0);

    // Check for existing job with same idempotency key
    if (args.idempotencyKey) {
      const existing = await ctx.db
        .query("jobs")
        .withIndex("by_idempotency", (q) => q.eq("idempotency_key", args.idempotencyKey))
        .first();

      if (existing && existing.status !== "failed" && existing.status !== "cancelled") {
        return existing._id;
      }
    }

    return await ctx.db.insert("jobs", {
      type: args.type,
      workspaceId: args.workspaceId,
      payload: args.payload,
      status: "queued",
      attempt: 0,
      scheduled_for_ms: scheduledFor,
      idempotency_key: args.idempotencyKey,
    });
  },
});

// Schedule mention ingestion job
export const scheduleIngestMention = internalAction({
  args: {
    mentionId: v.id("mention_events"),
    workspaceId: v.id("workspaces"),
  },
  returns: v.null(),
  handler: async (ctx, { mentionId, workspaceId }) => {
    await ctx.runMutation(internal.jobs.scheduleJob, {
      type: "ingest_mention",
      workspaceId,
      payload: { mentionId },
      idempotencyKey: `ingest_${mentionId}`,
    });
  },
});

// Process mention ingestion job
export const processIngestMention = internalAction({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.null(),
  handler: async (ctx, { jobId }) => {
    const job = await ctx.runQuery(internal.jobs.getJob, { jobId });
    if (!job || job.status !== "queued") return;

    try {
      await ctx.runMutation(internal.jobs.markJobRunning, { jobId });

      const { mentionId } = job.payload;
      
      // Get the mention
      const mention = await ctx.runQuery(internal.threads.getMention, { mentionId });
      if (!mention) {
        throw new Error("Mention not found");
      }

      // Create or get thread
      const threadId = await ctx.runMutation(internal.threads.createThread, {
        workspaceId: job.workspaceId,
        botAccountId: mention.botAccountId,
        rootTweetId: mention.conversation_id || mention.tweet_id,
        mentionEventId: mentionId,
      });

      // Schedule classification job
      await ctx.runMutation(internal.jobs.scheduleJob, {
        type: "classify",
        workspaceId: job.workspaceId,
        payload: { threadId },
        idempotencyKey: `classify_${threadId}`,
      });

      // Mark mention as processed
      await ctx.runMutation(internal.mentions.markMentionProcessed, { mentionId });

      await ctx.runMutation(internal.jobs.markJobCompleted, { jobId });
    } catch (error) {
      await ctx.runMutation(internal.jobs.markJobFailed, {
        jobId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

// Process classification job
export const processClassification = internalAction({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.null(),
  handler: async (ctx, { jobId }) => {
    const job = await ctx.runQuery(internal.jobs.getJob, { jobId });
    if (!job || job.status !== "queued") return;

    try {
      await ctx.runMutation(internal.jobs.markJobRunning, { jobId });

      const { threadId } = job.payload;
      
      // Get thread and agent profile
      const thread = await ctx.runQuery(api.threads.getThread, { threadId });
      if (!thread) {
        throw new Error("Thread not found");
      }

      const agentProfile = await ctx.runQuery(api.admin.getDefaultAgentProfile, {
        workspaceId: job.workspaceId,
      });

      if (!agentProfile) {
        throw new Error("No agent profile found");
      }

      // Run classification via agents
      const classification = await ctx.runAction(internal.agents.classifyMention, {
        threadId,
        agentProfileId: agentProfile._id,
      });

      // Update thread with classification results
      await ctx.runMutation(internal.threads.updateThread, {
        threadId,
        decision: classification.decision,
        reason: classification.reason,
        policy: classification.policy,
      });

      // If auto-reply, schedule compose job
      if (classification.decision === "auto_reply") {
        await ctx.runMutation(internal.jobs.scheduleJob, {
          type: "compose_answer",
          workspaceId: job.workspaceId,
          payload: { threadId },
          idempotencyKey: `compose_${threadId}`,
        });
      }

      await ctx.runMutation(internal.jobs.markJobCompleted, { jobId });
    } catch (error) {
      await ctx.runMutation(internal.jobs.markJobFailed, {
        jobId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

// Process compose answer job
export const processComposeAnswer = internalAction({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.null(),
  handler: async (ctx, { jobId }) => {
    const job = await ctx.runQuery(internal.jobs.getJob, { jobId });
    if (!job || job.status !== "queued") return;

    try {
      await ctx.runMutation(internal.jobs.markJobRunning, { jobId });

      const { threadId } = job.payload;
      
      // Generate AI response
      const response = await ctx.runAction(internal.agents.generateResponse, {
        threadId,
      });

      // Create reply draft
      const replyId = await ctx.runMutation(internal.replies.createReply, {
        threadId,
        parentTweetId: response.parentTweetId,
        text: response.text,
      });

      // Schedule post reply job
      await ctx.runMutation(internal.jobs.scheduleJob, {
        type: "post_reply",
        workspaceId: job.workspaceId,
        payload: { replyId },
        idempotencyKey: `post_${replyId}`,
      });

      await ctx.runMutation(internal.jobs.markJobCompleted, { jobId });
    } catch (error) {
      await ctx.runMutation(internal.jobs.markJobFailed, {
        jobId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

// Process post reply job
export const processPostReply = internalAction({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.null(),
  handler: async (ctx, { jobId }) => {
    const job = await ctx.runQuery(internal.jobs.getJob, { jobId });
    if (!job || job.status !== "queued") return;

    try {
      await ctx.runMutation(internal.jobs.markJobRunning, { jobId });

      const { replyId } = job.payload;
      
      // Post to Twitter
      const result = await ctx.runAction(internal.twitter.postReply, { replyId });

      if (result.success && result.tweetId) {
        // Update reply with posted tweet ID
        await ctx.runMutation(internal.replies.markReplyPosted, {
          replyId,
          tweetId: result.tweetId,
        });

        // Update thread status
        const reply = await ctx.runQuery(internal.replies.getReply, { replyId });
        if (reply) {
          await ctx.runMutation(internal.threads.markThreadResponded, {
            threadId: reply.threadId,
          });
        }
      } else {
        throw new Error(result.error || "Failed to post reply");
      }

      await ctx.runMutation(internal.jobs.markJobCompleted, { jobId });
    } catch (error) {
      await ctx.runMutation(internal.jobs.markJobFailed, {
        jobId,
        error: error instanceof Error ? error.message : "Unknown error",
      });

      // Mark reply as failed
      const { replyId } = job.payload;
      await ctx.runMutation(internal.replies.markReplyFailed, {
        replyId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

// Get job details
export const getJob = internalQuery({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.any(),
  handler: async (ctx, { jobId }) => {
    return await ctx.db.get(jobId);
  },
});

// Mark job as running
export const markJobRunning = internalMutation({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.null(),
  handler: async (ctx, { jobId }) => {
    await ctx.db.patch(jobId, {
      status: "running",
      started_at_ms: Date.now(),
    });
  },
});

// Mark job as completed
export const markJobCompleted = internalMutation({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.null(),
  handler: async (ctx, { jobId }) => {
    await ctx.db.patch(jobId, {
      status: "succeeded",
      finished_at_ms: Date.now(),
    });
  },
});

// Mark job as failed
export const markJobFailed = internalMutation({
  args: {
    jobId: v.id("jobs"),
    error: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, { jobId, error }) => {
    const job = await ctx.db.get(jobId);
    if (!job) return;

    const nextAttempt = job.attempt + 1;
    const maxAttempts = 3;

    if (nextAttempt < maxAttempts) {
      // Exponential backoff: 2^attempt seconds
      const delayMs = Math.pow(2, nextAttempt) * 1000;
      
      await ctx.db.patch(jobId, {
        status: "queued",
        attempt: nextAttempt,
        scheduled_for_ms: Date.now() + delayMs,
        error,
      });
    } else {
      await ctx.db.patch(jobId, {
        status: "failed",
        finished_at_ms: Date.now(),
        error,
      });
    }
  },
});

// Get queued jobs for processing
export const getQueuedJobs = internalQuery({
  args: {
    type: v.optional(v.union(
      v.literal("ingest_mention"),
      v.literal("classify"),
      v.literal("compose_answer"),
      v.literal("post_reply"),
      v.literal("backfill_mentions")
    )),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { type, limit = 10 }) => {
    let query = ctx.db
      .query("jobs")
      .filter((q) => {
        const statusFilter = q.eq(q.field("status"), "queued");
        if (type) {
          return q.and(q.eq(q.field("type"), type), statusFilter);
        }
        return statusFilter;
      })
      .filter((q) => q.lte(q.field("scheduled_for_ms"), Date.now()));

    return await query.take(limit);
  },
});

// Process job queue (called by scheduled function)
export const processJobQueue = internalAction({
  args: {
    jobType: v.optional(v.union(
      v.literal("ingest_mention"),
      v.literal("classify"),
      v.literal("compose_answer"),
      v.literal("post_reply"),
      v.literal("backfill_mentions")
    )),
  },
  returns: v.object({
    processed: v.number(),
    errors: v.number(),
  }),
  handler: async (ctx, { jobType }) => {
    const jobs = await ctx.runQuery(internal.jobs.getQueuedJobs, {
      type: jobType,
      limit: 5, // Process 5 jobs at a time
    });

    const results = {
      processed: 0,
      errors: 0,
    };

    for (const job of jobs) {
      try {
        switch (job.type) {
          case "ingest_mention":
            await ctx.runAction(internal.jobs.processIngestMention, { jobId: job._id });
            break;
          case "classify":
            await ctx.runAction(internal.jobs.processClassification, { jobId: job._id });
            break;
          case "compose_answer":
            await ctx.runAction(internal.jobs.processComposeAnswer, { jobId: job._id });
            break;
          case "post_reply":
            await ctx.runAction(internal.jobs.processPostReply, { jobId: job._id });
            break;
          case "backfill_mentions":
            await ctx.runAction(internal.jobs.processBackfillMentions, { jobId: job._id });
            break;
        }
        results.processed++;
      } catch (error) {
        console.error(`Error processing job ${job._id}:`, error);
        results.errors++;
      }
    }

    return results;
  },
});

// Process pending jobs of specific type
export const processPendingJobs = internalAction({
  args: {
    jobType: v.union(
      v.literal("ingest_mention"),
      v.literal("classify"),
      v.literal("compose_answer"),
      v.literal("post_reply"),
      v.literal("backfill_mentions")
    ),
  },
  returns: v.object({
    processed: v.number(),
    errors: v.number(),
  }),
  handler: async (ctx, { jobType }): Promise<{ processed: number; errors: number; }> => {
    return await ctx.runAction(internal.jobs.processJobQueue, { jobType });
  },
});

// Process backfill mentions job
export const processBackfillMentions = internalAction({
  args: {
    jobId: v.id("jobs"),
  },
  returns: v.null(),
  handler: async (ctx, { jobId }) => {
    const job = await ctx.runQuery(internal.jobs.getJob, { jobId });
    if (!job || job.status !== "queued") return;

    try {
      await ctx.runMutation(internal.jobs.markJobRunning, { jobId });

      // Get all bot accounts and poll for mentions
      const workspaces = await ctx.runQuery(api.admin.getWorkspaces, {});
      
      let totalProcessed = 0;
      for (const workspace of workspaces) {
        const botAccounts = await ctx.runQuery(api.admin.getBotAccounts, {
          workspaceId: workspace._id,
        });

        for (const botAccount of botAccounts) {
          if (botAccount.auto_reply_enabled) {
            const result = await ctx.runAction(internal.twitter.fetchMentions, {
              workspaceId: workspace._id,
              botAccountId: botAccount._id,
            });
            
            if (result.success) {
              totalProcessed += result.processedCount || 0;
            }
          }
        }
      }

      await ctx.runMutation(internal.jobs.markJobCompleted, { jobId });
      console.log(`Backfill mentions completed: ${totalProcessed} mentions processed`);

    } catch (error) {
      await ctx.runMutation(internal.jobs.markJobFailed, {
        jobId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
      logError("jobs.processBackfillMentions.error", { jobId, error: error instanceof Error ? error.message : String(error) });
    }
  },
});

// Get all workspaces (helper function)
export const getWorkspaces = internalAction({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx): Promise<any[]> => {
    return await ctx.runQuery(api.admin.getWorkspaces, {});
  },
});