"use node";

import { v } from "convex/values";
import { internalAction, internalMutation, internalQuery } from "./_generated/server";
import { api, internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { logInfo, logError, logDebug } from "./util/log";

// Post reply to Twitter
export const postReply = internalAction({
  args: {
    replyId: v.id("replies"),
  },
  returns: v.object({
    success: v.boolean(),
    tweetId: v.optional(v.string()),
    url: v.optional(v.string()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, { replyId }) => {
    const reply: any = await ctx.runQuery(internal.replies.getReply, { replyId });
    if (!reply) {
      return { success: false, error: "Reply not found" };
    }

    const thread: any = await ctx.runQuery(api.threads.getThread, {
      threadId: reply.threadId
    });
    if (!thread) {
      return { success: false, error: "Thread not found" };
    }

    const botAccount: any = await ctx.runQuery(api.admin.getBotAccount, {
      botAccountId: thread.botAccountId
    });
    if (!botAccount) {
      return { success: false, error: "Bot account not found" };
    }

    try {
      // Enforce bot rate limits before posting
      const now = Date.now();
      const counts = await ctx.runQuery(internal.rate.getCounts, {
        botAccountId: thread.botAccountId,
        nowMs: now,
        minuteWindowMs: 60_000,
        dayWindowMs: 86_400_000,
      });

      const minuteCount = counts.minute;
      const dayCount = counts.day;
      logInfo("twitter.postReply.rate_check", { minuteCount, minuteLimit: botAccount.per_minute_limit, dayCount, dayLimit: botAccount.per_day_limit });

      if (minuteCount >= botAccount.per_minute_limit) {
        return { success: false, error: "Rate limited per minute" };
      }
      if (dayCount >= botAccount.per_day_limit) {
        return { success: false, error: "Rate limited per day" };
      }

      const apiKey = process.env.TWITTER_API_KEY;
      if (!apiKey) {
        throw new Error("TWITTER_API_KEY not configured");
      }

      // Post tweet using docs.twitterapi.io
      const response: any = await fetch("https://internal.twitterapi.io/twitter/create_tweet_v2", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          text: reply.text,
          reply_to_tweet_id: reply.parent_tweet_id,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Twitter API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data: any = await response.json();

      if (data.success && data.data?.id) {
        // Cache the posted tweet
        await ctx.runMutation(internal.cache.cacheTwitterData, {
          workspaceId: thread.workspaceId,
          tweetId: data.data.id,
          data: data.data,
        });
        // increment counters on success
        await ctx.runMutation(internal.rate.increment, { botAccountId: thread.botAccountId, nowMs: now, windowMs: 60_000 });
        await ctx.runMutation(internal.rate.increment, { botAccountId: thread.botAccountId, nowMs: now, windowMs: 86_400_000 });


        return {
          success: true,
          tweetId: data.data.id,
          url: `https://twitter.com/${botAccount.username}/status/${data.data.id}`,
        };
      } else {
        throw new Error(data.error || "Unknown error from Twitter API");
      }

    } catch (error) {
      logError("twitter.postReply.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

// Fetch mentions from Twitter API
export const fetchMentions = internalAction({
  args: {
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
    sinceId: v.optional(v.string()),
    maxResults: v.optional(v.number()),
  },
  returns: v.object({
    success: v.boolean(),
    processedCount: v.optional(v.number()),
    totalFetched: v.optional(v.number()),
    lastTweetId: v.optional(v.string()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, { workspaceId, botAccountId, sinceId, maxResults = 25 }) => {
    const botAccount: any = await ctx.runQuery(api.admin.getBotAccount, { botAccountId });
    if (!botAccount) {
      return { success: false, error: "Bot account not found" };
    }

    try {
      const apiKey = process.env.TWITTER_API_KEY;
      if (!apiKey) {
        throw new Error("TWITTER_API_KEY not configured");
      }

      // Build query parameters
      const params = new URLSearchParams({
        screen_name: botAccount.username,
        count: maxResults.toString(),
        include_rts: "false",
        result_type: "recent",
      });

      if (sinceId) {
        params.append("since_id", sinceId);
      }

      const response = await fetch(`https://internal.twitterapi.io/twitter/user/mentions?${params}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Twitter API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (!data.success || !data.data) {
        throw new Error(data.error || "No data returned from Twitter API");
      }

      const mentions = [];

      for (const tweet of data.data) {
        // Extract URLs from entities
        const urls = [];
        if (tweet.entities?.urls) {
          for (const urlEntity of tweet.entities.urls) {
            urls.push(urlEntity.expanded_url || urlEntity.url);
          }
        }

        mentions.push({
          id: tweet.id_str,
          conversation_id: tweet.conversation_id,
          in_reply_to_status_id: tweet.in_reply_to_status_id_str,
          author_id: tweet.user.id_str,
          author_username: tweet.user.screen_name,
          text: tweet.full_text || tweet.text,
          urls,
          lang: tweet.lang,
          created_at: tweet.created_at,
          raw: tweet,
        });
      }

      // Process mentions
      let processedCount = 0;
      for (const mention of mentions) {
        try {
          await ctx.runMutation(internal.mentions.createMention, {
            workspaceId,
            botAccountId,
            tweetId: mention.id,
            conversationId: mention.conversation_id,
            parentTweetId: mention.in_reply_to_status_id,
            authorUserId: mention.author_id,
            authorUsername: mention.author_username,
            text: mention.text,
            urls: mention.urls,
            lang: mention.lang,
            source: "poll" as const,
            raw: mention.raw,
          });
          processedCount++;
        } catch (error) {
          console.error(`Error processing mention ${mention.id}:`, error);
        }
      }

      return {
        success: true,
        processedCount,
        totalFetched: mentions.length,
        lastTweetId: mentions.length > 0 ? mentions[mentions.length - 1].id : null,
      };

    } catch (error) {
      logError("twitter.fetchMentions.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

// Set up webhook rule for mentions
export const setupWebhookRule = internalAction({
  args: {
    workspaceId: v.id("workspaces"),
    botUsername: v.string(),
    tag: v.optional(v.string()),
  },
  returns: v.object({
    success: v.boolean(),
    ruleId: v.optional(v.string()),
    tag: v.optional(v.string()),
    value: v.optional(v.string()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, { workspaceId, botUsername, tag = "mentions" }) => {
    try {
      const apiKey = process.env.TWITTER_API_KEY;
      if (!apiKey) {
        throw new Error("TWITTER_API_KEY not configured");
      }

      // Create webhook rule to track mentions
      const response = await fetch("https://internal.twitterapi.io/oapi/tweet_filter/add_rule", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          tag,
          value: `@${botUsername}`,
          active: true,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Twitter API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (data.success && data.data?.id) {
        // Store webhook rule in database (call mutation in non-node module)
        logInfo("twitter.setupWebhookRule.save_rule", {
          workspaceId,
          ruleId: data.data.id,
          tag,
          value: `@${botUsername}`,
        });
        await ctx.runMutation(internal.twitter_mutations.saveWebhookRule, {
          workspaceId,
          ruleId: data.data.id,
          tag,
          value: `@${botUsername}`,
          intervalSeconds: 30, // Default polling interval
        });

        return {
          success: true,
          ruleId: data.data.id,
          tag,
          value: `@${botUsername}`,
        };
      } else {
        throw new Error(data.error || "Failed to create webhook rule");
      }

    } catch (error) {
      logError("twitter.setupWebhookRule.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});



// Validate Twitter credentials
export const validateCredentials = internalAction({
  args: {
    botUsername: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
    userInfo: v.optional(v.any()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, { botUsername }) => {
    try {
      const apiKey = process.env.TWITTER_API_KEY;
      if (!apiKey) {
        throw new Error("TWITTER_API_KEY not configured");
      }

      // Try to fetch user info to validate credentials
      const response = await fetch(`https://internal.twitterapi.io/twitter/user/info?screen_name=${botUsername}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Twitter API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        return {
          success: true,
          userInfo: {
            id: data.data.id_str,
            username: data.data.screen_name,
            displayName: data.data.name,
            avatarUrl: data.data.profile_image_url_https,
            verified: data.data.verified,
            followersCount: data.data.followers_count,
          },
        };
      } else {
        throw new Error(data.error || "Failed to validate credentials");
      }

    } catch (error) {
      logError("twitter.validateCredentials.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

// Get rate limit status
export const getRateLimitStatus = internalAction({
  args: {},
  returns: v.object({
    success: v.boolean(),
    rateLimits: v.optional(v.any()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx) => {
    try {
      const apiKey = process.env.TWITTER_API_KEY;
      if (!apiKey) {
        throw new Error("TWITTER_API_KEY not configured");
      }

      const response = await fetch("https://internal.twitterapi.io/twitter/rate_limit_status", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Twitter API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      return {
        success: true,
        rateLimits: data.data || {},
      };

    } catch (error) {
      logError("twitter.getRateLimitStatus.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});