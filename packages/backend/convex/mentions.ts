import { v } from "convex/values";
import { action, internalMutation, internalQuery, mutation, query } from "./_generated/server";
import { api, internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

// Query mentions for a workspace
export const getMentions = query({
  args: {
    workspaceId: v.id("workspaces"),
    limit: v.optional(v.number()),
    processed: v.optional(v.boolean()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, limit = 50, processed }) => {
    let query = ctx.db
      .query("mention_events")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId))
      .order("desc");

    if (processed !== undefined) {
      query = query.filter((q) => q.eq(q.field("processed"), processed));
    }

    return await query.take(limit);
  },
});

// Get a specific mention by tweet ID
export const getMentionByTweetId = query({
  args: {
    botAccountId: v.id("bot_accounts"),
    tweetId: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { botAccountId, tweetId }) => {
    return await ctx.db
      .query("mention_events")
      .withIndex("by_bot_tweet", (q) => 
        q.eq("botAccountId", botAccountId).eq("tweet_id", tweetId)
      )
      .first();
  },
});

// Create a new mention event
export const createMention = internalMutation({
  args: {
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
    tweetId: v.string(),
    conversationId: v.optional(v.string()),
    parentTweetId: v.optional(v.string()),
    authorUserId: v.string(),
    authorUsername: v.string(),
    text: v.string(),
    urls: v.array(v.string()),
    lang: v.optional(v.string()),
    source: v.union(v.literal("webhook"), v.literal("poll")),
    raw: v.any(),
  },
  returns: v.id("mention_events"),
  handler: async (ctx, args) => {
    // Check if mention already exists
    const existing = await ctx.db
      .query("mention_events")
      .withIndex("by_bot_tweet", (q) => 
        q.eq("botAccountId", args.botAccountId).eq("tweet_id", args.tweetId)
      )
      .first();

    if (existing) {
      return existing._id;
    }

    // Create new mention event
    const mentionId = await ctx.db.insert("mention_events", {
      workspaceId: args.workspaceId,
      botAccountId: args.botAccountId,
      tweet_id: args.tweetId,
      conversation_id: args.conversationId,
      parent_tweet_id: args.parentTweetId,
      author_user_id: args.authorUserId,
      author_username: args.authorUsername,
      text: args.text,
      urls: args.urls,
      lang: args.lang,
      created_at_ms: Date.now(),
      processed: false,
      source: args.source,
      raw: args.raw,
    });

    // Schedule processing job
    await ctx.scheduler.runAfter(0, internal.jobs.scheduleIngestMention, {
      mentionId,
      workspaceId: args.workspaceId,
    });

    return mentionId;
  },
});

// Mark mention as processed
export const markMentionProcessed = internalMutation({
  args: {
    mentionId: v.id("mention_events"),
  },
  returns: v.null(),
  handler: async (ctx, { mentionId }) => {
    await ctx.db.patch(mentionId, {
      processed: true,
    });
  },
});

// Poll Twitter API for mentions (called by scheduled function)
export const pollMentions = action({
  args: {
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
  },
  handler: async (ctx, { workspaceId, botAccountId }) => {
    // Get bot account details
    const botAccount = await ctx.runQuery(api.admin.getBotAccount, { 
      botAccountId 
    });
    
    if (!botAccount || !botAccount.auto_reply_enabled) {
      return { success: false, reason: "Bot account disabled or not found" };
    }

    // Get the last processed mention timestamp
    const lastMention = await ctx.runQuery(api.mentions.getLastMention, {
      botAccountId,
    });

    const sinceId = lastMention?.tweet_id;

    try {
      // Call Twitter API to get mentions
      const mentions = await fetchTwitterMentions(
        botAccount.username,
        sinceId
      );

      let processedCount = 0;
      
      for (const mention of mentions) {
        await ctx.runMutation(internal.mentions.createMention, {
          workspaceId,
          botAccountId,
          tweetId: mention.id,
          conversationId: mention.conversation_id,
          parentTweetId: mention.in_reply_to_status_id,
          authorUserId: mention.author_id,
          authorUsername: mention.author_username,
          text: mention.text,
          urls: mention.urls || [],
          lang: mention.lang,
          source: "poll" as const,
          raw: mention,
        });
        processedCount++;
      }

      return {
        success: true,
        processedCount,
        lastTweetId: mentions[mentions.length - 1]?.id,
      };
    } catch (error) {
      console.error("Error polling mentions:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

// Get last mention for bot account (for polling cursor)
export const getLastMention = query({
  args: {
    botAccountId: v.id("bot_accounts"),
  },
  returns: v.any(),
  handler: async (ctx, { botAccountId }) => {
    return await ctx.db
      .query("mention_events")
      .withIndex("by_bot_tweet", (q) => q.eq("botAccountId", botAccountId))
      .order("desc")
      .first();
  },
});

// Search mentions by text
export const searchMentions = query({
  args: {
    workspaceId: v.id("workspaces"),
    searchText: v.string(),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, searchText, limit = 20 }) => {
    const results = await ctx.db
      .query("mention_events")
      .withSearchIndex("search_text", (q) => q.search("text", searchText))
      .filter((q) => q.eq(q.field("workspaceId"), workspaceId))
      .take(limit);

    return results;
  },
});

// Helper function to fetch Twitter mentions (implementation depends on API)
async function fetchTwitterMentions(username: string, sinceId?: string): Promise<any[]> {
  const apiKey = process.env.TWITTER_API_KEY;
  if (!apiKey) {
    throw new Error("Twitter API key not configured");
  }

  // This would be replaced with actual Twitter API call
  // For now, returning mock data structure
  const mockMentions: any[] = [];
  
  // TODO: Implement actual Twitter API call to docs.twitterapi.io
  // Example: GET /twitter/user/mentions?screen_name=${username}&since_id=${sinceId}
  
  return mockMentions;
}