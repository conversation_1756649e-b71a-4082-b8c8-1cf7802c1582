"use node";

import { v } from "convex/values";
import { internalAction, internalQuery } from "./_generated/server";
import { api, internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { logInfo, logError } from "./util/log";

// Search with Exa API
export const searchWithExa = internalAction({
  args: {
    query: v.string(),
    numResults: v.optional(v.number()),
    workspaceId: v.id("workspaces"),
    useText: v.optional(v.boolean()),
  },
  returns: v.object({
    results: v.array(v.any()),
    autoprompt: v.optional(v.string()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, { query, numResults = 3, workspaceId, useText = true }): Promise<{ results: any[]; autoprompt?: string; error?: string; }> => {
    const { createHash } = await import("crypto");
    const queryHash = createHash("md5").update(query).digest("hex");
    
    // Check cache first
    const cached: any = await ctx.runQuery(internal.queries.getExaCache, {
      workspaceId,
      queryHash,
    });

    if (cached && cached.ttl_ms > Date.now()) {
      return cached.results;
    }

    try {
      const apiKey = process.env.EXA_API_KEY;
      if (!apiKey) {
        throw new Error("EXA_API_KEY not configured");
      }

      const response = await fetch("https://api.exa.ai/search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          query,
          num_results: numResults,
          text: useText,
          highlights: true,
        }),
      });

      if (!response.ok) {
        logError("tools.searchWithExa.http_error", { status: response.status, statusText: response.statusText });
        throw new Error(`Exa API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const results = {
        results: data.results || [],
        autoprompt: data.autoprompt || null,
      };

      // Cache the results (TTL: 1 hour)
      await ctx.runMutation(internal.cache.cacheExaResults, {
        workspaceId,
        queryHash,
        query,
        data: results,
        ttlMs: Date.now() + (process.env.EXA_CACHE_TTL ? parseInt(process.env.EXA_CACHE_TTL) : 3600000),
      });
      logInfo("tools.searchWithExa.cached", { workspaceId, queryHash });

      return results;
    } catch (error) {
      logError("tools.searchWithExa.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        error: error instanceof Error ? error.message : "Unknown error",
        results: [],
      };
    }
  },
});

// Scrape with Firecrawl API
export const scrapeWithFirecrawl = internalAction({
  args: {
    url: v.string(),
    formats: v.optional(v.array(v.string())),
    workspaceId: v.id("workspaces"),
  },
  returns: v.object({
    success: v.boolean(),
    data: v.any(),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, { url, formats = ["markdown"], workspaceId }): Promise<{ success: boolean; data: any; error?: string; }> => {
    const { createHash } = await import("crypto");
    const urlHash = createHash("md5").update(url + formats.join(",")).digest("hex");
    
    // Check cache first
    const cached: any = await ctx.runQuery(internal.queries.getFirecrawlCache, {
      workspaceId,
      urlHash,
    });

    if (cached && cached.ttl_ms > Date.now()) {
      return cached.data;
    }

    try {
      const apiKey = process.env.FIRECRAWL_API_KEY;
      if (!apiKey) {
        throw new Error("FIRECRAWL_API_KEY not configured");
      }

      const response = await fetch("https://api.firecrawl.dev/v1/scrape", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          url,
          formats,
          onlyMainContent: true,
          includeTags: ["h1", "h2", "h3", "p", "li", "blockquote"],
          excludeTags: ["nav", "footer", "aside", "script", "style"],
        }),
      });

      if (!response.ok) {
        logError("tools.scrapeWithFirecrawl.http_error", { status: response.status, statusText: response.statusText });
        throw new Error(`Firecrawl API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const result = {
        success: data.success || false,
        data: data.data || null,
        error: data.error || null,
      };

      // Cache successful results (TTL: 2 hours)
      if (result.success) {
        await ctx.runMutation(internal.cache.cacheFirecrawlResults, {
          workspaceId,
          urlHash,
          url,
          formats,
          data: result,
          ttlMs: Date.now() + (process.env.FIRECRAWL_CACHE_TTL ? parseInt(process.env.FIRECRAWL_CACHE_TTL) : 7200000),
        });
        logInfo("tools.scrapeWithFirecrawl.cached", { workspaceId, urlHash });
      }

      return result;
    } catch (error) {
      logError("tools.scrapeWithFirecrawl.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        data: null,
      };
    }
  },
});

// Search with xAI Live Search
export const searchWithXAI = internalAction({
  args: {
    query: v.string(),
    maxResults: v.optional(v.number()),
    workspaceId: v.id("workspaces"),
    sources: v.optional(v.array(v.string())),
    safeSearch: v.optional(v.boolean()),
  },
  returns: v.object({
    success: v.boolean(),
    response: v.string(),
    citations: v.array(v.any()),
    searchResults: v.array(v.any()),
    usage: v.any(),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, { query, maxResults = 5, workspaceId, sources = ["x"], safeSearch = true }) => {
    try {
      const apiKey = process.env.XAI_API_KEY;
      if (!apiKey) {
        throw new Error("XAI_API_KEY not configured");
      }

      const response = await fetch("https://api.x.ai/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          model: "grok-beta",
          messages: [
            {
              role: "user",
              content: query,
            },
          ],
          search_parameters: {
            mode: "on",
            max_search_results: maxResults,
            sources: sources.map(s => ({ type: s })),
            safe_search: safeSearch,
            return_citations: true,
          },
        }),
      });

      if (!response.ok) {
        logError("tools.searchWithXAI.http_error", { status: response.status, statusText: response.statusText });
        throw new Error(`xAI API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      const result = {
        success: true,
        response: data.choices?.[0]?.message?.content || "",
        citations: data.citations || [],
        searchResults: data.search_results || [],
        usage: data.usage || null,
      };

      logInfo("tools.searchWithXAI.success", { usage: result.usage });
      return result;
    } catch (error) {
      logError("tools.searchWithXAI.error", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        response: "",
        citations: [],
        searchResults: [],
        usage: {},
      };
    }
  },
});












// Clean expired cache entries (called by scheduled function)
export const cleanExpiredCaches = internalAction({
  args: {},
  returns: v.object({
    exaCleaned: v.number(),
    firecrawlCleaned: v.number(),
    twitterCleaned: v.number(),
  }),
  handler: async (ctx) => {
    const now = Date.now();
    
    const exaCleaned: number = await ctx.runMutation(internal.cache.cleanExpiredExaCache, { now });
    const firecrawlCleaned: number = await ctx.runMutation(internal.cache.cleanExpiredFirecrawlCache, { now });
    const twitterCleaned: number = await ctx.runMutation(internal.cache.cleanExpiredTwitterCache, { cutoffMs: now - 86400000 });

    return {
      exaCleaned,
      firecrawlCleaned,
      twitterCleaned,
    };
  },
});