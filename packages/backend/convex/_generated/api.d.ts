/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin from "../admin.js";
import type * as agents from "../agents.js";
import type * as cache from "../cache.js";
import type * as healthCheck from "../healthCheck.js";
import type * as http from "../http.js";
import type * as jobs from "../jobs.js";
import type * as mentions from "../mentions.js";
import type * as metrics from "../metrics.js";
import type * as queries from "../queries.js";
import type * as rate from "../rate.js";
import type * as replies from "../replies.js";
import type * as scheduled from "../scheduled.js";
import type * as threads from "../threads.js";
import type * as tools from "../tools.js";
import type * as twitter from "../twitter.js";
import type * as twitter_mutations from "../twitter_mutations.js";
import type * as util_log from "../util/log.js";
import type * as util_webhook from "../util/webhook.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  agents: typeof agents;
  cache: typeof cache;
  healthCheck: typeof healthCheck;
  http: typeof http;
  jobs: typeof jobs;
  mentions: typeof mentions;
  metrics: typeof metrics;
  queries: typeof queries;
  rate: typeof rate;
  replies: typeof replies;
  scheduled: typeof scheduled;
  threads: typeof threads;
  tools: typeof tools;
  twitter: typeof twitter;
  twitter_mutations: typeof twitter_mutations;
  "util/log": typeof util_log;
  "util/webhook": typeof util_webhook;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
