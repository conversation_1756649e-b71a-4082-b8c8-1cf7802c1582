import { v } from "convex/values";
import { internalMutation, internalQuery, query } from "./_generated/server";
import type { Id } from "./_generated/dataModel";

// Get reply by ID
export const getReply = internalQuery({
  args: {
    replyId: v.id("replies"),
  },
  returns: v.any(),
  handler: async (ctx, { replyId }) => {
    return await ctx.db.get(replyId);
  },
});

// Get replies for thread
export const getThreadReplies = query({
  args: {
    threadId: v.id("threads"),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { threadId }) => {
    return await ctx.db
      .query("replies")
      .withIndex("by_thread_status", (q) => q.eq("threadId", threadId))
      .collect();
  },
});

// Get replies for workspace
export const getReplies = query({
  args: {
    workspaceId: v.id("workspaces"),
    status: v.optional(v.union(
      v.literal("draft"),
      v.literal("posted"),
      v.literal("failed")
    )),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, status, limit = 50 }) => {
    // First get threads for the workspace
    const threads = await ctx.db
      .query("threads")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId))
      .collect();

    const threadIds = threads.map(t => t._id);

    // Get replies for those threads
    const allReplies = [];
    for (const threadId of threadIds) {
      let query = ctx.db
        .query("replies")
        .withIndex("by_thread_status", (q) => q.eq("threadId", threadId));

      if (status) {
        query = query.filter((q) => q.eq(q.field("status"), status));
      }

      const replies = await query.collect();
      allReplies.push(...replies);
    }

    // Sort by creation time (use posted_at_ms or fallback to thread creation)
    allReplies.sort((a, b) => {
      const timeA = a.posted_at_ms || 0;
      const timeB = b.posted_at_ms || 0;
      return timeB - timeA;
    });

    return allReplies.slice(0, limit);
  },
});

// Create reply
export const createReply = internalMutation({
  args: {
    threadId: v.id("threads"),
    parentTweetId: v.string(),
    text: v.string(),
  },
  returns: v.id("replies"),
  handler: async (ctx, { threadId, parentTweetId, text }) => {
    return await ctx.db.insert("replies", {
      threadId,
      parent_tweet_id: parentTweetId,
      text,
      status: "draft",
    });
  },
});

// Mark reply as posted
export const markReplyPosted = internalMutation({
  args: {
    replyId: v.id("replies"),
    tweetId: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, { replyId, tweetId }) => {
    await ctx.db.patch(replyId, {
      reply_tweet_id: tweetId,
      status: "posted",
      posted_at_ms: Date.now(),
    });
  },
});

// Mark reply as failed
export const markReplyFailed = internalMutation({
  args: {
    replyId: v.id("replies"),
    error: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, { replyId, error }) => {
    await ctx.db.patch(replyId, {
      status: "failed",
      error,
    });
  },
});

// Update reply text
export const updateReplyText = internalMutation({
  args: {
    replyId: v.id("replies"),
    text: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, { replyId, text }) => {
    await ctx.db.patch(replyId, {
      text,
    });
  },
});

// Get reply statistics
export const getReplyStats = query({
  args: {
    workspaceId: v.id("workspaces"),
    dateRange: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, dateRange }) => {
    // Get all threads for workspace
    const threads = await ctx.db
      .query("threads")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId))
      .collect();

    const threadIds = threads.map(t => t._id);
    
    // Get all replies for those threads
    const allReplies = [];
    for (const threadId of threadIds) {
      const replies = await ctx.db
        .query("replies")
        .withIndex("by_thread_status", (q) => q.eq("threadId", threadId))
        .collect();
      allReplies.push(...replies);
    }

    // Filter by date range if provided
    let filteredReplies = allReplies;
    if (dateRange) {
      filteredReplies = allReplies.filter(reply => {
        const timestamp = reply.posted_at_ms || 0;
        return timestamp >= dateRange.start && timestamp <= dateRange.end;
      });
    }

    const stats = {
      total: filteredReplies.length,
      draft: 0,
      posted: 0,
      failed: 0,
      successRate: 0,
    };

    for (const reply of filteredReplies) {
      stats[reply.status as keyof typeof stats]++;
    }

    if (stats.total > 0) {
      stats.successRate = (stats.posted / stats.total) * 100;
    }

    return stats;
  },
});

// Get recent successful replies with engagement data
export const getRecentReplies = query({
  args: {
    workspaceId: v.id("workspaces"),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, limit = 10 }) => {
    // Get threads for workspace
    const threads = await ctx.db
      .query("threads")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId))
      .order("desc")
      .collect();

    const results = [];
    
    for (const thread of threads) {
      const replies = await ctx.db
        .query("replies")
        .withIndex("by_thread_status", (q) => q.eq("threadId", thread._id))
        .filter((q) => q.eq(q.field("status"), "posted"))
        .collect();

      for (const reply of replies) {
        results.push({
          reply,
          thread,
        });
      }

      if (results.length >= limit) break;
    }

    // Sort by posted time
    results.sort((a, b) => {
      const timeA = a.reply.posted_at_ms || 0;
      const timeB = b.reply.posted_at_ms || 0;
      return timeB - timeA;
    });

    return results.slice(0, limit);
  },
});