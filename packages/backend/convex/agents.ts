"use node";

import { v } from "convex/values";
import { internalAction } from "./_generated/server";
import { api, internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { generateText, tool } from "ai";
import { createOpenAI } from "@ai-sdk/openai";
import { z } from "zod";
import { logInfo, logError } from "./util/log";

// Classification result schema
const ClassificationSchema = z.object({
  decision: z.enum(["auto_reply", "skip", "manual_review"]),
  reason: z.string(),
  intent: z.string(),
  requires_web_search: z.boolean(),
  requires_url_scraping: z.boolean(),
  requires_twitter_search: z.boolean(),
  policy: z.object({
    allow_twitter_search: z.boolean(),
    allow_web_search: z.boolean(),
    allow_firecrawl: z.boolean(),
    safe_search_web_news: z.boolean(),
  }),
});

// Classify mention intent and determine reply strategy
export const classifyMention = internalAction({
  args: {
    threadId: v.id("threads"),
    agentProfileId: v.id("agent_profiles"),
  },
  returns: v.object({
    decision: v.union(v.literal("auto_reply"), v.literal("skip"), v.literal("manual_review")),
    reason: v.string(),
    intent: v.string(),
    requires_web_search: v.boolean(),
    requires_url_scraping: v.boolean(),
    requires_twitter_search: v.boolean(),
    policy: v.object({
      allow_twitter_search: v.boolean(),
      allow_web_search: v.boolean(),
      allow_firecrawl: v.boolean(),
      safe_search_web_news: v.boolean(),
    }),
  }),
  handler: async (ctx, { threadId, agentProfileId }) => {
    // Get thread details and mention
    const thread: any = await ctx.runQuery(api.threads.getThread, { threadId });
    if (!thread) throw new Error("Thread not found");

    const mention: any = await ctx.runQuery(internal.threads.getMention, {
      mentionId: thread.last_mention_event_id,
    });
    if (!mention) throw new Error("Mention not found");

    const agentProfile: any = await ctx.runQuery(api.admin.getAgentProfile, {
      profileId: agentProfileId,
    });
    if (!agentProfile) throw new Error("Agent profile not found");

    // Check policy rules first
    const policyRules: any[] = await ctx.runQuery(api.admin.getPolicyRules, {
      workspaceId: thread.workspaceId,
    });

    // Check for blocked users or keywords
    for (const rule of policyRules) {
      if (rule.ruleType === "block_user" && 
          (mention.author_username === rule.value || mention.author_user_id === rule.value)) {
        return {
          decision: "skip" as const,
          reason: `User ${mention.author_username} is blocked`,
          intent: "blocked_user",
          requires_web_search: false,
          requires_url_scraping: false,
          requires_twitter_search: false,
          policy: {
            allow_twitter_search: false,
            allow_web_search: false,
            allow_firecrawl: false,
            safe_search_web_news: true,
          },
        };
      }

      if (rule.ruleType === "block_keyword" && 
          mention.text.toLowerCase().includes(rule.value.toLowerCase())) {
        return {
          decision: "skip" as const,
          reason: `Contains blocked keyword: ${rule.value}`,
          intent: "blocked_keyword",
          requires_web_search: false,
          requires_url_scraping: false,
          requires_twitter_search: false,
          policy: {
            allow_twitter_search: false,
            allow_web_search: false,
            allow_firecrawl: false,
            safe_search_web_news: true,
          },
        };
      }
    }

    try {
      // Create OpenRouter client
      const openrouter = createOpenAI({
        baseURL: "https://openrouter.ai/api/v1",
        apiKey: process.env.OPENROUTER_API_KEY,
      });

      // Use small model for classification
      const result = await generateText({
        model: openrouter("gpt-4o-mini"),
        system: `You are a classification agent for AskBuddy, a Twitter bot. Analyze mentions and determine the best response strategy.

Guidelines:
- auto_reply: For legitimate questions, requests for information, or helpful interactions
- skip: For spam, promotional content, inappropriate content, or off-topic mentions  
- manual_review: For complex, sensitive, or ambiguous cases

Analyze the mention text and determine:
1. The user's intent
2. Whether web search is needed for current information
3. Whether URL scraping is needed (if URLs are present)
4. Whether Twitter search is needed for Twitter-specific context
5. Appropriate tool policies

Be conservative - when in doubt, choose manual_review over auto_reply.`,
        prompt: `Analyze this Twitter mention:

Author: @${mention.author_username}
Text: ${mention.text}
URLs: ${mention.urls.join(", ") || "None"}
Language: ${mention.lang || "unknown"}

Classify this mention and determine the appropriate response strategy.`,
        tools: {
          classify: tool({
            description: "Classify the mention and set response policy",
            inputSchema: ClassificationSchema,
            execute: async (params) => params,
          }),
        },
        toolChoice: "required",
      });

      const toolCall = result.toolCalls[0];
      if (toolCall?.toolName === "classify") {
        const classification = toolCall.input as {
          decision: "auto_reply" | "skip" | "manual_review";
          reason: string;
          intent: string;
          requires_web_search: boolean;
          requires_url_scraping: boolean;
          requires_twitter_search: boolean;
          policy: {
            allow_twitter_search: boolean;
            allow_web_search: boolean;
            allow_firecrawl: boolean;
            safe_search_web_news: boolean;
          };
        };
        
        // Log the classification
        await ctx.runMutation(internal.threads.addMessage, {
          threadId,
          role: "assistant",
          content: `Classification: ${classification.decision} - ${classification.reason}`,
          model: "gpt-4o-mini",
          tokenUsage: {
            input: result.usage?.inputTokens || 0,
            output: result.usage?.outputTokens || 0,
          },
        });

        return classification;
      }

      throw new Error("Classification tool not called properly");
    } catch (error) {
      logError("agents.classifyMention.error", { error: error instanceof Error ? error.message : String(error) });

      // Fallback classification
      return {
        decision: "manual_review" as const,
        reason: "Classification failed - requires manual review",
        intent: "unknown",
        requires_web_search: false,
        requires_url_scraping: mention.urls.length > 0,
        requires_twitter_search: false,
        policy: {
          allow_twitter_search: true,
          allow_web_search: true,
          allow_firecrawl: mention.urls.length > 0,
          safe_search_web_news: true,
        },
      };
    }
  },
});

// Generate AI response for mention
export const generateResponse = internalAction({
  args: {
    threadId: v.id("threads"),
  },
  returns: v.object({
    text: v.string(),
    parentTweetId: v.string(),
    toolCalls: v.number(),
    tokenUsage: v.object({
      inputTokens: v.number(),
      outputTokens: v.number(),
    }),
  }),
  handler: async (ctx, { threadId }) => {
    // Get thread details
    const thread: any = await ctx.runQuery(api.threads.getThread, { threadId });
    if (!thread) throw new Error("Thread not found");

    const mention: any = await ctx.runQuery(internal.threads.getMention, {
      mentionId: thread.last_mention_event_id,
    });
    if (!mention) throw new Error("Mention not found");

    const agentProfile: any = await ctx.runQuery(api.admin.getDefaultAgentProfile, {
      workspaceId: thread.workspaceId,
    });
    if (!agentProfile) throw new Error("No agent profile found");

    // Get existing messages for context
    const messages: any[] = await ctx.runQuery(api.threads.getThreadMessages, { threadId });

    try {
      // Create OpenRouter client
      const openrouter = createOpenAI({
        baseURL: "https://openrouter.ai/api/v1",
        apiKey: process.env.OPENROUTER_API_KEY,
      });

      // Build conversation history
      const conversationMessages: any[] = [
        {
          role: "system" as const,
          content: agentProfile.system_prompt,
        },
        {
          role: "user" as const,
          content: `User @${mention.author_username} mentioned me with: "${mention.text}"`,
        },
        // Add any previous messages
        ...messages.filter((m: any) => m.role !== "system").map((m: any) => ({
          role: m.role as any,
          content: m.content,
        })),
      ];

      // Define available tools based on thread policy
      const availableTools: Record<string, any> = {};

      if (thread.policy.allow_web_search && agentProfile.tool_defaults.exa_enabled) {
        availableTools.searchWeb = tool({
          description: "Search the web for current information",
          inputSchema: z.object({
            query: z.string(),
            numResults: z.number().optional().default(3),
          }),
          execute: async ({ query, numResults = 3 }) => {
            return await ctx.runAction(internal.tools.searchWithExa, {
              query,
              numResults,
              workspaceId: thread.workspaceId,
            });
          },
        });
      }

      if (thread.policy.allow_firecrawl && agentProfile.tool_defaults.firecrawl_enabled && mention.urls.length > 0) {
        availableTools.scrapeUrl = tool({
          description: "Scrape content from URLs",
          inputSchema: z.object({
            url: z.string(),
            formats: z.array(z.string()).optional().default(["markdown"]),
          }),
          execute: async ({ url, formats = ["markdown"] }) => {
            return await ctx.runAction(internal.tools.scrapeWithFirecrawl, {
              url,
              formats,
              workspaceId: thread.workspaceId,
            });
          },
        });
      }

      if (thread.policy.allow_twitter_search && agentProfile.tool_defaults.xai_live_search_enabled) {
        availableTools.searchTwitter = tool({
          description: "Search Twitter/X for recent posts",
          inputSchema: z.object({
            query: z.string(),
            maxResults: z.number().optional().default(agentProfile.tool_defaults.xai_search_max_results),
          }),
          execute: async ({ query, maxResults = 5 }) => {
            return await ctx.runAction(internal.tools.searchWithXAI, {
              query,
              maxResults,
              workspaceId: thread.workspaceId,
            });
          },
        });
      }

      // Generate response using primary model
      const result: any = await generateText({
        model: openrouter(agentProfile.model_primary),
        messages: conversationMessages,
        tools: availableTools,
        toolChoice: "auto",
        temperature: agentProfile.temperature,
        topP: agentProfile.top_p,
      });

      // Log tool calls if any were made
      if (result.toolCalls && result.toolCalls.length > 0) {
        for (const toolCall of result.toolCalls) {
          await ctx.runMutation(internal.threads.addMessage, {
            threadId,
            role: "tool",
            content: `Tool: ${toolCall.toolName}\nArgs: ${JSON.stringify(toolCall.input)}\nResult: Tool executed successfully`,
            toolName: toolCall.toolName,
            toolArgs: toolCall.input,
            toolResult: "executed",
          });
        }
      }

      // Log the final response
      await ctx.runMutation(internal.threads.addMessage, {
        threadId,
        role: "assistant",
        content: result.text,
        model: agentProfile.model_primary,
        tokenUsage: {
          input: result.usage?.inputTokens || 0,
          output: result.usage?.outputTokens || 0,
        },
      });

      // Format response for Twitter (ensure it fits character limits)
      let responseText: string = result.text;
      if (responseText.length > 280) {
        responseText = responseText.substring(0, 277) + "...";
      }

      return {
        text: responseText,
        parentTweetId: mention.tweet_id,
        toolCalls: result.toolCalls?.length || 0,
        tokenUsage: { 
          inputTokens: result.usage?.inputTokens || 0, 
          outputTokens: result.usage?.outputTokens || 0 
        },
      };

    } catch (error) {
      logError("agents.generateResponse.error", { error: error instanceof Error ? error.message : String(error) });

      // Log error
      await ctx.runMutation(internal.threads.addMessage, {
        threadId,
        role: "assistant",
        content: `Error generating response: ${error instanceof Error ? error.message : "Unknown error"}`,
      });

      // Fallback response
      return {
        text: "Sorry, I encountered an error while processing your request. Please try again later.",
        parentTweetId: mention.tweet_id,
        toolCalls: 0,
        tokenUsage: { inputTokens: 0, outputTokens: 0 },
      };
    }
  },
});

// Helper to get agent profile (used in jobs.ts)
export const getAgentProfile = internalAction({
  args: {
    profileId: v.id("agent_profiles"),
  },
  returns: v.any(),
  handler: async (ctx, { profileId }) => {
    const profiles: any[] = await ctx.runQuery(api.admin.getAgentProfiles, { 
      // This is a workaround since we don't have a direct getById query
      workspaceId: "dummy" as any 
    });
    return profiles.find(p => p._id === profileId);
  },
});