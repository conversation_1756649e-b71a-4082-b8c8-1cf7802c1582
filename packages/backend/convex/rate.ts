import { v } from "convex/values";
import { internalMutation, internalQuery } from "./_generated/server";
import type { Id } from "./_generated/dataModel";

function floorWindow(now: number, windowMs: number) {
  return Math.floor(now / windowMs) * windowMs;
}

export const getCounts = internalQuery({
  args: {
    botAccountId: v.id("bot_accounts"),
    nowMs: v.number(),
    minuteWindowMs: v.literal(60_000),
    dayWindowMs: v.literal(86_400_000),
  },
  returns: v.object({ minute: v.number(), day: v.number() }),
  handler: async (ctx, { botAccountId, nowMs, minuteWindowMs, dayWindowMs }) => {
    const minuteStart = floorWindow(nowMs, minuteWindowMs);
    const dayStart = floorWindow(nowMs, dayWindowMs);

    const [minuteDoc, dayDoc] = await Promise.all([
      ctx.db
        .query("rate_counters")
        .withIndex("by_bot_window", (q) => q
          .eq("botAccountId", botAccountId)
          .eq("window_start_ms", minuteStart)
          .eq("window_ms", minuteWindowMs)
        )
        .first(),
      ctx.db
        .query("rate_counters")
        .withIndex("by_bot_window", (q) => q
          .eq("botAccountId", botAccountId)
          .eq("window_start_ms", dayStart)
          .eq("window_ms", dayWindowMs)
        )
        .first(),
    ]);

    return { minute: minuteDoc?.count || 0, day: dayDoc?.count || 0 };
  }
});

export const increment = internalMutation({
  args: {
    botAccountId: v.id("bot_accounts"),
    nowMs: v.number(),
    windowMs: v.union(v.literal(60_000), v.literal(86_400_000)),
  },
  returns: v.null(),
  handler: async (ctx, { botAccountId, nowMs, windowMs }) => {
    const start = floorWindow(nowMs, windowMs);
    const existing = await ctx.db
      .query("rate_counters")
      .withIndex("by_bot_window", (q) => q
        .eq("botAccountId", botAccountId)
        .eq("window_start_ms", start)
        .eq("window_ms", windowMs)
      )
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, { count: existing.count + 1 });
    } else {
      await ctx.db.insert("rate_counters", {
        botAccountId,
        window_start_ms: start,
        window_ms: windowMs,
        count: 1,
      });
    }
    return null;
  }
});

