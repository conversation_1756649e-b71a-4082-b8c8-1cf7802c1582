import { cronJobs } from "convex/server";
import { internal } from "./_generated/api";

const crons = cronJobs();

// Poll for mentions every 30 seconds
crons.interval(
  "poll-mentions",
  { seconds: 30 },
  internal.jobs.processPendingJobs,
  { jobType: "backfill_mentions" }
);

// Process queued jobs every 10 seconds
crons.interval(
  "process-jobs",
  { seconds: 10 },
  internal.jobs.processJobQueue,
  {}
);

// Clean expired caches daily at 2 AM
crons.cron(
  "clean-caches",
  "0 2 * * *",
  internal.tools.cleanExpiredCaches,
  {}
);

// Aggregate daily metrics at midnight
crons.cron(
  "aggregate-metrics",
  "0 0 * * *",
  internal.metrics.aggregateDailyMetrics
);

export default crons;