import { v } from "convex/values";
import { internalAction, internalMutation, query } from "./_generated/server";
import { api, internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

// Aggregate daily metrics for all workspaces
export const aggregateDailyMetrics = internalAction({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    // Get all workspaces
    const workspaces = await ctx.runQuery(api.admin.getWorkspaces, {});
    
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const startOfDay = new Date(today + 'T00:00:00.000Z').getTime();
    const endOfDay = new Date(today + 'T23:59:59.999Z').getTime();

    for (const workspace of workspaces) {
      await ctx.runMutation(internal.metrics.aggregateWorkspaceMetrics, {
        workspaceId: workspace._id,
        dateUtc: today,
        startOfDay,
        endOfDay,
      });
    }
  },
});

// Aggregate metrics for a specific workspace
export const aggregateWorkspaceMetrics = internalMutation({
  args: {
    workspaceId: v.id("workspaces"),
    dateUtc: v.string(),
    startOfDay: v.number(),
    endOfDay: v.number(),
  },
  returns: v.null(),
  handler: async (ctx, { workspaceId, dateUtc, startOfDay, endOfDay }) => {
  // Check if metrics already exist for this date
  const existingMetrics = await ctx.db
    .query("daily_metrics")
    .withIndex("by_workspace_day", (q: any) => 
      q.eq("workspaceId", workspaceId).eq("date_utc", dateUtc)
    )
    .first();

  // Get mentions count
  const mentions = await ctx.db
    .query("mention_events")
    .withIndex("by_workspace_time", (q: any) => q.eq("workspaceId", workspaceId))
    .filter((q: any) => 
      q.gte(q.field("created_at_ms"), startOfDay) &&
      q.lte(q.field("created_at_ms"), endOfDay)
    )
    .collect();

  // Get replies count
  const threads = await ctx.db
    .query("threads")
    .withIndex("by_workspace_time", (q: any) => q.eq("workspaceId", workspaceId))
    .filter((q: any) => 
      q.gte(q.field("created_at_ms"), startOfDay) &&
      q.lte(q.field("created_at_ms"), endOfDay)
    )
    .collect();

  const repliedThreads = threads.filter((t: any) => t.status === "responded");

  // Calculate average latency (time from mention to reply)
  let totalLatency = 0;
  let latencyCount = 0;

  for (const thread of repliedThreads) {
    const mention = await ctx.db.get(thread.last_mention_event_id);
    if (mention) {
      const latency = thread.updated_at_ms - mention.created_at_ms;
      totalLatency += latency;
      latencyCount++;
    }
  }

  const avgLatency = latencyCount > 0 ? totalLatency / latencyCount : 0;

  // Get token usage from messages
  const messages = await ctx.db
    .query("messages")
    .filter((q: any) => 
      q.gte(q.field("created_at_ms"), startOfDay) &&
      q.lte(q.field("created_at_ms"), endOfDay)
    )
    .collect();

  // Filter messages by workspace through their threads
  const workspaceMessages = [];
  for (const message of messages) {
    const thread = await ctx.db.get(message.threadId);
    if (thread && thread.workspaceId === workspaceId) {
      workspaceMessages.push(message);
    }
  }

  let tokenIn = 0;
  let tokenOut = 0;
  for (const message of workspaceMessages) {
    if (message.token_usage) {
      tokenIn += message.token_usage.input || 0;
      tokenOut += message.token_usage.output || 0;
    }
  }

  // Estimate costs (rough calculation based on GPT pricing)
  // GPT-4o-mini: ~$0.15/1M input tokens, ~$0.60/1M output tokens
  const costsUsdEst = (tokenIn * 0.15 / 1000000) + (tokenOut * 0.60 / 1000000);

  const metrics = {
    workspaceId,
    date_utc: dateUtc,
    mentions: mentions.length,
    replies_posted: repliedThreads.length,
    avg_latency_ms: Math.round(avgLatency),
    token_in: tokenIn,
    token_out: tokenOut,
    costs_usd_est: Math.round(costsUsdEst * 100) / 100, // Round to 2 decimal places
  };

  if (existingMetrics) {
    // Update existing metrics
    await ctx.db.patch(existingMetrics._id, metrics);
  } else {
    // Create new metrics record
    await ctx.db.insert("daily_metrics", metrics);
  }
  },
});

// Get metrics for dashboard
export const getMetricsSummary = query({
  args: {
    workspaceId: v.id("workspaces"),
    days: v.optional(v.number()),
  },
  handler: async (ctx, { workspaceId, days = 7 }) => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const metrics = await ctx.db
      .query("daily_metrics")
      .withIndex("by_workspace_day", (q) => q.eq("workspaceId", workspaceId))
      .filter((q) => 
        q.gte(q.field("date_utc"), startDate.toISOString().split('T')[0]) &&
        q.lte(q.field("date_utc"), endDate.toISOString().split('T')[0])
      )
      .collect();

    // Calculate totals and averages
    const summary = {
      totalMentions: 0,
      totalReplies: 0,
      avgLatency: 0,
      totalTokensIn: 0,
      totalTokensOut: 0,
      totalCosts: 0,
      successRate: 0,
      dailyBreakdown: metrics,
    };

    if (metrics.length > 0) {
      summary.totalMentions = metrics.reduce((sum, m) => sum + m.mentions, 0);
      summary.totalReplies = metrics.reduce((sum, m) => sum + m.replies_posted, 0);
      summary.avgLatency = Math.round(
        metrics.reduce((sum, m) => sum + m.avg_latency_ms, 0) / metrics.length
      );
      summary.totalTokensIn = metrics.reduce((sum, m) => sum + m.token_in, 0);
      summary.totalTokensOut = metrics.reduce((sum, m) => sum + m.token_out, 0);
      summary.totalCosts = Math.round(
        metrics.reduce((sum, m) => sum + m.costs_usd_est, 0) * 100
      ) / 100;
      summary.successRate = summary.totalMentions > 0 
        ? Math.round((summary.totalReplies / summary.totalMentions) * 100 * 10) / 10
        : 0;
    }

    return summary;
  },
});

// Get real-time stats
export const getRealTimeStats = query({
  args: {
    workspaceId: v.id("workspaces"),
  },
  handler: async (ctx, { workspaceId }) => {
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    // Get recent mentions
    const recentMentions = await ctx.db
      .query("mention_events")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId))
      .filter((q) => q.gte(q.field("created_at_ms"), last24Hours))
      .collect();

    // Get thread stats
    const threads = await ctx.db
      .query("threads")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId))
      .filter((q) => q.gte(q.field("created_at_ms"), last24Hours))
      .collect();

    const stats = {
      pending: 0,
      responded: 0,
      skipped: 0,
      failed: 0,
    };

    for (const thread of threads) {
      stats[thread.status as keyof typeof stats]++;
    }

    // Get recent jobs
    const recentJobs = await ctx.db
      .query("jobs")
      .withIndex("by_workspace_status_time", (q) => q.eq("workspaceId", workspaceId))
      .filter((q) => q.gte(q.field("scheduled_for_ms"), last24Hours))
      .take(10);

    return {
      mentions24h: recentMentions.length,
      processed24h: recentMentions.filter(m => m.processed).length,
      threadStats: stats,
      recentJobs: recentJobs.length,
    };
  },
});