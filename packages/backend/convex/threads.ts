import { v } from "convex/values";
import { internalMutation, internalQuery, mutation, query } from "./_generated/server";
import type { Id } from "./_generated/dataModel";

// Get thread by ID
export const getThread = query({
  args: {
    threadId: v.id("threads"),
  },
  returns: v.any(),
  handler: async (ctx, { threadId }) => {
    return await ctx.db.get(threadId);
  },
});

// Get threads for workspace
export const getThreads = query({
  args: {
    workspaceId: v.id("workspaces"),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("responded"),
      v.literal("skipped"),
      v.literal("failed")
    )),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, status, limit = 50 }) => {
    let query = ctx.db
      .query("threads")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId))
      .order("desc");

    if (status) {
      query = query.filter((q) => q.eq(q.field("status"), status));
    }

    return await query.take(limit);
  },
});

// Get thread with messages
export const getThreadWithMessages = query({
  args: {
    threadId: v.id("threads"),
  },
  returns: v.any(),
  handler: async (ctx, { threadId }) => {
    const thread = await ctx.db.get(threadId);
    if (!thread) return null;

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_thread_time", (q) => q.eq("threadId", threadId))
      .order("asc")
      .collect();

    const replies = await ctx.db
      .query("replies")
      .withIndex("by_thread_status", (q) => q.eq("threadId", threadId))
      .collect();

    return {
      ...thread,
      messages,
      replies,
    };
  },
});

// Create thread
export const createThread = internalMutation({
  args: {
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
    rootTweetId: v.string(),
    mentionEventId: v.id("mention_events"),
  },
  returns: v.id("threads"),
  handler: async (ctx, args) => {
    // Check if thread already exists for this root tweet
    const existing = await ctx.db
      .query("threads")
      .filter((q) => 
        q.eq(q.field("workspaceId"), args.workspaceId) &&
        q.eq(q.field("root_tweet_id"), args.rootTweetId)
      )
      .first();

    if (existing) {
      // Update with latest mention event
      await ctx.db.patch(existing._id, {
        last_mention_event_id: args.mentionEventId,
        updated_at_ms: Date.now(),
      });
      return existing._id;
    }

    // Create new thread
    return await ctx.db.insert("threads", {
      workspaceId: args.workspaceId,
      botAccountId: args.botAccountId,
      root_tweet_id: args.rootTweetId,
      last_mention_event_id: args.mentionEventId,
      status: "pending",
      decision: "auto_reply",
      policy: {
        allow_twitter_search: true,
        allow_web_search: true,
        allow_firecrawl: true,
        safe_search_web_news: true,
      },
      model: "openai/gpt-4o-mini",
      created_at_ms: Date.now(),
      updated_at_ms: Date.now(),
    });
  },
});

// Update thread
export const updateThread = internalMutation({
  args: {
    threadId: v.id("threads"),
    decision: v.optional(v.union(
      v.literal("auto_reply"),
      v.literal("skip"),
      v.literal("manual_review")
    )),
    reason: v.optional(v.string()),
    policy: v.optional(v.object({
      allow_twitter_search: v.boolean(),
      allow_web_search: v.boolean(),
      allow_firecrawl: v.boolean(),
      safe_search_web_news: v.boolean(),
    })),
    model: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const updates: any = {
      updated_at_ms: Date.now(),
    };

    if (args.decision !== undefined) {
      updates.decision = args.decision;
    }
    if (args.reason !== undefined) {
      updates.reason = args.reason;
    }
    if (args.policy !== undefined) {
      updates.policy = args.policy;
    }
    if (args.model !== undefined) {
      updates.model = args.model;
    }

    await ctx.db.patch(args.threadId, updates);
  },
});

// Mark thread as responded
export const markThreadResponded = internalMutation({
  args: {
    threadId: v.id("threads"),
  },
  returns: v.null(),
  handler: async (ctx, { threadId }) => {
    await ctx.db.patch(threadId, {
      status: "responded",
      updated_at_ms: Date.now(),
    });
  },
});

// Mark thread as skipped
export const markThreadSkipped = internalMutation({
  args: {
    threadId: v.id("threads"),
    reason: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, { threadId, reason }) => {
    await ctx.db.patch(threadId, {
      status: "skipped",
      reason,
      updated_at_ms: Date.now(),
    });
  },
});

// Mark thread as failed
export const markThreadFailed = internalMutation({
  args: {
    threadId: v.id("threads"),
    reason: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, { threadId, reason }) => {
    await ctx.db.patch(threadId, {
      status: "failed",
      reason,
      updated_at_ms: Date.now(),
    });
  },
});

// Add message to thread
export const addMessage = internalMutation({
  args: {
    threadId: v.id("threads"),
    role: v.union(v.literal("system"), v.literal("user"), v.literal("assistant"), v.literal("tool")),
    content: v.string(),
    toolName: v.optional(v.string()),
    toolArgs: v.optional(v.any()),
    toolResult: v.optional(v.any()),
    model: v.optional(v.string()),
    tokenUsage: v.optional(v.object({
      input: v.optional(v.number()),
      output: v.optional(v.number()),
    })),
  },
  returns: v.id("messages"),
  handler: async (ctx, args) => {
    return await ctx.db.insert("messages", {
      threadId: args.threadId,
      role: args.role,
      content: args.content,
      tool_name: args.toolName,
      tool_args: args.toolArgs,
      tool_result: args.toolResult,
      model: args.model,
      token_usage: args.tokenUsage,
      created_at_ms: Date.now(),
    });
  },
});

// Get messages for thread
export const getThreadMessages = query({
  args: {
    threadId: v.id("threads"),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { threadId }) => {
    return await ctx.db
      .query("messages")
      .withIndex("by_thread_time", (q) => q.eq("threadId", threadId))
      .order("asc")
      .collect();
  },
});

// Get mention event for thread (helper)
export const getMention = internalQuery({
  args: {
    mentionId: v.id("mention_events"),
  },
  returns: v.any(),
  handler: async (ctx, { mentionId }) => {
    return await ctx.db.get(mentionId);
  },
});

// Search messages content
export const searchMessages = query({
  args: {
    workspaceId: v.id("workspaces"),
    searchText: v.string(),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, searchText, limit = 20 }) => {
    // First find matching messages
    const messages = await ctx.db
      .query("messages")
      .withSearchIndex("search_content", (q) => q.search("content", searchText))
      .take(limit);

    // Get associated threads and filter by workspace
    const results = [];
    for (const message of messages) {
      const thread = await ctx.db.get(message.threadId);
      if (thread && thread.workspaceId === workspaceId) {
        results.push({
          message,
          thread,
        });
      }
    }

    return results;
  },
});

// Get thread statistics for workspace
export const getThreadStats = query({
  args: {
    workspaceId: v.id("workspaces"),
    dateRange: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId, dateRange }) => {
    let query = ctx.db
      .query("threads")
      .withIndex("by_workspace_time", (q) => q.eq("workspaceId", workspaceId));

    if (dateRange) {
      query = query.filter((q) => 
        q.gte(q.field("created_at_ms"), dateRange.start) &&
        q.lte(q.field("created_at_ms"), dateRange.end)
      );
    }

    const threads = await query.collect();

    const stats = {
      total: threads.length,
      pending: 0,
      responded: 0,
      skipped: 0,
      failed: 0,
      byDecision: {
        auto_reply: 0,
        skip: 0,
        manual_review: 0,
      },
    };

    for (const thread of threads) {
      stats[thread.status as keyof typeof stats]++;
      if (thread.decision) {
        stats.byDecision[thread.decision as keyof typeof stats.byDecision]++;
      }
    }

    return stats;
  },
});