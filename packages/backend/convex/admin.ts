import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { logError } from "./util/log";

function requireAuth(ctx: any) {
  // In production, configure Convex auth and check identity
  const identity = (ctx as any).auth?.getUserIdentity?.();
  if (!identity) {
    throw new Error("Unauthorized");
  }
  return identity;
}

async function assertWorkspaceOwner(ctx: any, workspaceId: string) {
  const identity = requireAuth(ctx);
  const ws = await ctx.db.get(workspaceId);
  if (!ws || ws.ownerUserId !== identity.subject) {
    throw new Error("Forbidden");
  }
}

import type { Id } from "./_generated/dataModel";

// Get workspace by owner
export const getWorkspace = query({
  args: {
    ownerUserId: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, { ownerUserId }) => {
    return await ctx.db
      .query("workspaces")
      .withIndex("by_owner", (q) => q.eq("ownerUserId", ownerUserId))
      .first();
  },
});

// Create workspace
export const createWorkspace = mutation({
  args: {
    name: v.string(),
    ownerUserId: v.string(),
    timezone: v.optional(v.string()),
  },
  returns: v.id("workspaces"),
  handler: async (ctx, { name, ownerUserId, timezone }) => {
    requireAuth(ctx);
    // Check if workspace already exists
    const existing = await ctx.db
      .query("workspaces")
      .withIndex("by_owner", (q) => q.eq("ownerUserId", ownerUserId))
      .first();

    if (existing) {
      return existing._id;
    }

    return await ctx.db.insert("workspaces", {
      name,
      ownerUserId,
      settings: {
        timezone,
      },
    });
  },
});

// Get bot account
export const getBotAccount = query({
  args: {
    botAccountId: v.id("bot_accounts"),
  },
  returns: v.any(),
  handler: async (ctx, { botAccountId }) => {
    return await ctx.db.get(botAccountId);
  },
});

// Get bot accounts for workspace
export const getBotAccounts = query({
  args: {
    workspaceId: v.id("workspaces"),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId }) => {
    await assertWorkspaceOwner(ctx, workspaceId);
    return await ctx.db
      .query("bot_accounts")
      .withIndex("by_workspace", (q) => q.eq("workspaceId", workspaceId))
      .collect();
  },
});

// Create bot account
export const createBotAccount = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    xUserId: v.string(),
    username: v.string(),
    displayName: v.optional(v.string()),
    avatarUrl: v.optional(v.string()),
    provider: v.string(),
    keyLabel: v.optional(v.string()),
    keyLast4: v.optional(v.string()),
  },
  returns: v.id("bot_accounts"),
  handler: async (ctx, args) => {
    await assertWorkspaceOwner(ctx, args.workspaceId);
    return await ctx.db.insert("bot_accounts", {
      workspaceId: args.workspaceId,
      x_user_id: args.xUserId,
      username: args.username,
      display_name: args.displayName,
      avatar_url: args.avatarUrl,
      auto_reply_enabled: true,
      per_minute_limit: 5,
      per_day_limit: 100,
      credentials: {
        provider: args.provider,
        key_label: args.keyLabel,
        key_last4: args.keyLast4,
      },
    });
  },
});

// Update bot account settings
export const updateBotAccount = mutation({
  args: {
    botAccountId: v.id("bot_accounts"),
    autoReplyEnabled: v.optional(v.boolean()),
    perMinuteLimit: v.optional(v.number()),
    perDayLimit: v.optional(v.number()),
  },
  returns: v.null(),
  handler: async (ctx, { botAccountId, autoReplyEnabled, perMinuteLimit, perDayLimit }) => {
    requireAuth(ctx);
    const updates: any = {};

    if (autoReplyEnabled !== undefined) {
      updates.auto_reply_enabled = autoReplyEnabled;
    }
    if (perMinuteLimit !== undefined) {
      updates.per_minute_limit = perMinuteLimit;
    }
    if (perDayLimit !== undefined) {
      updates.per_day_limit = perDayLimit;
    }

    await ctx.db.patch(botAccountId, updates);
  },
});

// Get agent profiles
export const getAgentProfiles = query({
  args: {
    workspaceId: v.id("workspaces"),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId }) => {
    return await ctx.db
      .query("agent_profiles")
      .withIndex("by_workspace_name", (q) => q.eq("workspaceId", workspaceId))
      .collect();
  },
});

// Get specific agent profile by ID
export const getAgentProfile = query({
  args: {
    profileId: v.id("agent_profiles"),
  },
  returns: v.any(),
  handler: async (ctx, { profileId }) => {
    return await ctx.db.get(profileId);
  },
});

// Get default agent profile
export const getDefaultAgentProfile = query({
  args: {
    workspaceId: v.id("workspaces"),
  },
  returns: v.any(),
  handler: async (ctx, { workspaceId }) => {
    return await ctx.db
      .query("agent_profiles")
      .withIndex("by_workspace_name", (q) => q.eq("workspaceId", workspaceId))
      .first();
  },
});

// Create default agent profile
export const createDefaultAgentProfile = mutation({
  args: {
    workspaceId: v.id("workspaces"),
  },
  returns: v.id("agent_profiles"),
  handler: async (ctx, { workspaceId }) => {
    const defaultProfile = {
      workspaceId,
      name: "AskBuddy Default",
      system_prompt: `You are AskBuddy, a helpful Twitter bot that provides informative and accurate responses to mentions.

Your capabilities include:
- Searching the web for current information using Exa
- Scraping and analyzing web pages using Firecrawl
- Searching Twitter/X content using xAI Live Search
- Providing cited, well-researched answers

Guidelines:
- Always be helpful and informative
- Cite your sources when using external information
- Keep responses concise but comprehensive (under 280 characters when possible for Twitter)
- Be respectful and avoid controversial topics
- If you cannot find relevant information, say so honestly`,
      temperature: 0.7,
      top_p: 0.9,
      model_primary: "openai/gpt-4o-mini",
      model_fallbacks: ["openrouter/auto", "openai/gpt-4o-mini"],
      tool_defaults: {
        firecrawl_enabled: true,
        exa_enabled: true,
        xai_live_search_enabled: true,
        xai_search_max_results: 5,
        safe_search_web_news: true,
      },
      updated_at_ms: Date.now(),
    };

    return await ctx.db.insert("agent_profiles", defaultProfile);
  },
});

// Update agent profile
export const updateAgentProfile = mutation({
  args: {
    profileId: v.id("agent_profiles"),
    systemPrompt: v.optional(v.string()),
    temperature: v.optional(v.number()),
    topP: v.optional(v.number()),
    modelPrimary: v.optional(v.string()),
    modelFallbacks: v.optional(v.array(v.string())),
    toolDefaults: v.optional(v.object({
      firecrawl_enabled: v.boolean(),
      exa_enabled: v.boolean(),
      xai_live_search_enabled: v.boolean(),
      xai_search_max_results: v.number(),
      safe_search_web_news: v.boolean(),
    })),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const updates: any = {
      updated_at_ms: Date.now(),
    };

    if (args.systemPrompt !== undefined) {
      updates.system_prompt = args.systemPrompt;
    }
    if (args.temperature !== undefined) {
      updates.temperature = args.temperature;
    }
    if (args.topP !== undefined) {
      updates.top_p = args.topP;
    }
    if (args.modelPrimary !== undefined) {
      updates.model_primary = args.modelPrimary;
    }
    if (args.modelFallbacks !== undefined) {
      updates.model_fallbacks = args.modelFallbacks;
    }
    if (args.toolDefaults !== undefined) {
      updates.tool_defaults = args.toolDefaults;
    }

    await ctx.db.patch(args.profileId, updates);
  },
});

// Get provider keys
export const getProviderKeys = query({
  args: {
    workspaceId: v.id("workspaces"),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId }) => {
    return await ctx.db
      .query("provider_keys")
      .withIndex("by_workspace_provider", (q) => q.eq("workspaceId", workspaceId))
      .collect();
  },
});

// Add provider key metadata (actual key stored in env)
export const addProviderKey = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    provider: v.union(
      v.literal("openrouter"),
      v.literal("exa"),
      v.literal("firecrawl"),
      v.literal("xai_live_search"),
      v.literal("twitterapi_io")
    ),
    label: v.string(),
    last4: v.string(),
  },
  returns: v.id("provider_keys"),
  handler: async (ctx, { workspaceId, provider, label, last4 }) => {
    await assertWorkspaceOwner(ctx, workspaceId);
    return await ctx.db.insert("provider_keys", {
      workspaceId,
      provider,
      label,
      last4,
      created_at_ms: Date.now(),
      enabled: true,
    });
  },
});

// Get policy rules
export const getPolicyRules = query({
  args: {
    workspaceId: v.id("workspaces"),
    ruleType: v.optional(v.union(
      v.literal("block_user"),
      v.literal("block_keyword"),
      v.literal("allow_user")
    )),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, ruleType }) => {
    let query = ctx.db
      .query("policy_rules")
      .withIndex("by_workspace_type_value", (q) => q.eq("workspaceId", workspaceId));

    if (ruleType) {
      query = query.filter((q) => q.eq(q.field("ruleType"), ruleType));
    }

    return await query.collect();
  },
});

// Add policy rule
export const addPolicyRule = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    ruleType: v.union(
      v.literal("block_user"),
      v.literal("block_keyword"),
      v.literal("allow_user")
    ),
    value: v.string(),
    notes: v.optional(v.string()),
  },
  returns: v.id("policy_rules"),
  handler: async (ctx, { workspaceId, ruleType, value, notes }) => {
    await assertWorkspaceOwner(ctx, workspaceId);
    return await ctx.db.insert("policy_rules", {
      workspaceId,
      ruleType,
      value,
      notes,
      created_at_ms: Date.now(),
    });
  },
});

// Remove policy rule
export const removePolicyRule = mutation({
  args: {
    ruleId: v.id("policy_rules"),
  },
  returns: v.null(),
  handler: async (ctx, { ruleId }) => {
    requireAuth(ctx);
    await ctx.db.delete(ruleId);
  },
});

// Get daily metrics
export const getDailyMetrics = query({
  args: {
    workspaceId: v.id("workspaces"),
    dateRange: v.optional(v.object({
      start: v.string(),
      end: v.string(),
    })),
  },
  returns: v.array(v.any()),
  handler: async (ctx, { workspaceId, dateRange }) => {
    let query = ctx.db
      .query("daily_metrics")
      .withIndex("by_workspace_day", (q) => q.eq("workspaceId", workspaceId));

    if (dateRange) {
      query = query.filter((q) =>
        q.gte(q.field("date_utc"), dateRange.start) &&
        q.lte(q.field("date_utc"), dateRange.end)
      );
    }

    return await query.collect();
  },
});

// Get all workspaces
export const getWorkspaces = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    return await ctx.db.query("workspaces").collect();
  },
});