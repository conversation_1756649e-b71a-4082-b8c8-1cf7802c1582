import { v } from "convex/values";
import { internalMutation } from "./_generated/server";

/**
 * Save webhook rule to database (non-node module).
 * This must live outside of any "use node" file because Convex forbids
 * exporting mutations/queries from Node actions modules.
 */
export const saveWebhookRule = internalMutation({
  args: {
    workspaceId: v.id("workspaces"),
    ruleId: v.string(),
    tag: v.string(),
    value: v.string(),
    intervalSeconds: v.number(),
  },
  returns: v.id("webhook_rules"),
  handler: async (ctx, args) => {
    // Robust logging for debugging
    console.log(
      "[twitter_mutations.saveWebhookRule] inserting webhook rule",
      {
        workspaceId: args.workspaceId,
        ruleId: args.ruleId,
        tag: args.tag,
        value: args.value,
        intervalSeconds: args.intervalSeconds,
      }
    );

    const id = await ctx.db.insert("webhook_rules", {
      workspaceId: args.workspaceId,
      rule_id: args.ruleId,
      tag: args.tag,
      value: args.value,
      active: true,
      interval_seconds: args.intervalSeconds,
      created_at_ms: Date.now(),
      updated_at_ms: Date.now(),
    });

    console.log(
      "[twitter_mutations.saveWebhookRule] inserted webhook rule id",
      id
    );

    return id;
  },
});

