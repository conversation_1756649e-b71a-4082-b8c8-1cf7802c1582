import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export const providerKind = v.union(
  v.literal("openrouter"),
  v.literal("exa"),
  v.literal("firecrawl"),
  v.literal("xai_live_search"),
  v.literal("twitterapi_io")
);

export const jobStatus = v.union(
  v.literal("queued"),
  v.literal("running"),
  v.literal("succeeded"),
  v.literal("failed"),
  v.literal("cancelled")
);

export const replyDecision = v.union(
  v.literal("auto_reply"),
  v.literal("skip"),
  v.literal("manual_review")
);

// Multi-tenant in case you run multiple bots in one install
export default defineSchema({
  // --- Core domain ---
  workspaces: defineTable({
    name: v.string(),
    ownerUserId: v.string(), // app admin auth user id
    settings: v.object({
      timezone: v.optional(v.string()),
    }),
  }).index("by_owner", ["ownerUserId"]),

  bot_accounts: defineTable({
    workspaceId: v.id("workspaces"),
    x_user_id: v.string(),       // the bot's X user id
    username: v.string(),        // @AskBuddy
    display_name: v.optional(v.string()),
    avatar_url: v.optional(v.string()),
    // whether to auto reply on mentions
    auto_reply_enabled: v.boolean(),
    // throttle limits
    per_minute_limit: v.number(),
    per_day_limit: v.number(),
    // masked token metadata only
    credentials: v.object({
      provider: v.string(), // e.g., "docs.twitterapi.io"
      key_label: v.optional(v.string()),
      key_last4: v.optional(v.string()),
    }),
  }).index("by_workspace", ["workspaceId"]),

  // Mention events ingested from webhook/poller
  mention_events: defineTable({
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
    tweet_id: v.string(),
    conversation_id: v.optional(v.string()),
    parent_tweet_id: v.optional(v.string()),
    author_user_id: v.string(),
    author_username: v.string(),
    text: v.string(),
    urls: v.array(v.string()),
    lang: v.optional(v.string()),
    created_at_ms: v.number(),
    processed: v.boolean(), // quick flag
    source: v.union(v.literal("webhook"), v.literal("poll")),
    raw: v.any(), // original normalized payload
  })
    .index("by_bot_tweet", ["botAccountId", "tweet_id"])
    .index("by_workspace_time", ["workspaceId", "created_at_ms"])
    .searchIndex("search_text", { searchField: "text" }),

  // One logical conversation per mention (thread)
  threads: defineTable({
    workspaceId: v.id("workspaces"),
    botAccountId: v.id("bot_accounts"),
    root_tweet_id: v.string(),
    last_mention_event_id: v.id("mention_events"),
    status: v.union(
      v.literal("pending"),
      v.literal("responded"),
      v.literal("skipped"),
      v.literal("failed")
    ),
    decision: replyDecision,
    reason: v.optional(v.string()),
    policy: v.object({
      allow_twitter_search: v.boolean(),
      allow_web_search: v.boolean(),
      allow_firecrawl: v.boolean(),
      safe_search_web_news: v.boolean(), // xAI param
    }),
    // model used for the final answer
    model: v.string(), // e.g., "openai/gpt-5-mini"
    created_at_ms: v.number(),
    updated_at_ms: v.number(),
  })
    .index("by_bot_status_time", ["botAccountId", "status", "updated_at_ms"])
    .index("by_workspace_time", ["workspaceId", "created_at_ms"]),

  // LLM messages (for auditability)
  messages: defineTable({
    threadId: v.id("threads"),
    role: v.union(v.literal("system"), v.literal("user"), v.literal("assistant"), v.literal("tool")),
    content: v.string(), // stored as markdown or plain text
    tool_name: v.optional(v.string()),
    tool_args: v.optional(v.any()),
    tool_result: v.optional(v.any()),
    model: v.optional(v.string()),
    token_usage: v.optional(v.object({
      input: v.optional(v.number()),
      output: v.optional(v.number()),
    })),
    created_at_ms: v.number(),
  })
    .index("by_thread_time", ["threadId", "created_at_ms"])
    .searchIndex("search_content", { searchField: "content" }),

  // Final replies posted on X
  replies: defineTable({
    threadId: v.id("threads"),
    reply_tweet_id: v.optional(v.string()),
    parent_tweet_id: v.string(),
    text: v.string(),
    status: v.union(
      v.literal("draft"),
      v.literal("posted"),
      v.literal("failed")
    ),
    error: v.optional(v.string()),
    posted_at_ms: v.optional(v.number()),
  }).index("by_thread_status", ["threadId", "status"]),

  // Jobs for durable async processing
  jobs: defineTable({
    type: v.union(
      v.literal("ingest_mention"),
      v.literal("classify"),
      v.literal("compose_answer"),
      v.literal("post_reply"),
      v.literal("backfill_mentions")
    ),
    workspaceId: v.id("workspaces"),
    payload: v.any(),
    status: jobStatus,
    attempt: v.number(),
    scheduled_for_ms: v.number(),
    started_at_ms: v.optional(v.number()),
    finished_at_ms: v.optional(v.number()),
    idempotency_key: v.optional(v.string()),
    error: v.optional(v.string()),
  })
    .index("by_workspace_status_time", ["workspaceId", "status", "scheduled_for_ms"])
    .index("by_type_status", ["type", "status"])
    .index("by_idempotency", ["idempotency_key"]),

  // Tool caches to reduce latency/cost
  exa_cache: defineTable({
    workspaceId: v.id("workspaces"),
    query_hash: v.string(),
    query: v.string(),
    results: v.any(),
    created_at_ms: v.number(),
    ttl_ms: v.number(),
  }).index("by_workspace_qhash", ["workspaceId", "query_hash"]),

  firecrawl_cache: defineTable({
    workspaceId: v.id("workspaces"),
    url_hash: v.string(),
    url: v.string(),
    formats: v.array(v.string()), // ["markdown","html","json"]
    data: v.any(), // markdown/html/json payload
    created_at_ms: v.number(),
    ttl_ms: v.number(),
  }).index("by_workspace_urlhash", ["workspaceId", "url_hash"]),

  twitter_cache: defineTable({
    workspaceId: v.id("workspaces"),
    tweet_id: v.string(),
    data: v.any(),
    fetched_at_ms: v.number(),
  }).index("by_workspace_tweet", ["workspaceId", "tweet_id"]),

  // Settings & prompts
  agent_profiles: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(), // "AskBuddy default"
    system_prompt: v.string(),
    temperature: v.number(),
    top_p: v.optional(v.number()),
    model_primary: v.string(), // "openai/gpt-5-mini"
    model_fallbacks: v.array(v.string()),
    tool_defaults: v.object({
      firecrawl_enabled: v.boolean(),
      exa_enabled: v.boolean(),
      xai_live_search_enabled: v.boolean(),
      xai_search_max_results: v.number(),
      safe_search_web_news: v.boolean(),
    }),
    updated_at_ms: v.number(),
  }).index("by_workspace_name", ["workspaceId", "name"]),

  // API key metadata (masked)
  provider_keys: defineTable({
    workspaceId: v.id("workspaces"),
    provider: providerKind,
    label: v.string(),
    last4: v.optional(v.string()),
    created_at_ms: v.number(),
    enabled: v.boolean(),
  }).index("by_workspace_provider", ["workspaceId", "provider"]),

  // Moderation & policy
  moderation_flags: defineTable({
    threadId: v.id("threads"),
    category: v.string(), // "toxicity", "nsfw", etc.
    score: v.number(),    // 0..1
    notes: v.optional(v.string()),
    created_at_ms: v.number(),
  }).index("by_thread_category", ["threadId", "category"]),

  // Analytics (rollups)
  rate_counters: defineTable({
    botAccountId: v.id("bot_accounts"),
    window_start_ms: v.number(),
    window_ms: v.number(),
    count: v.number(),
  })
    .index("by_bot_window", ["botAccountId", "window_start_ms", "window_ms"]),

  daily_metrics: defineTable({
    workspaceId: v.id("workspaces"),
    date_utc: v.string(), // "2025-08-13"
    mentions: v.number(),
    replies_posted: v.number(),
    avg_latency_ms: v.number(),
    token_in: v.number(),
    token_out: v.number(),
    costs_usd_est: v.number(),
  }).index("by_workspace_day", ["workspaceId", "date_utc"]),

  // Blacklists / allowlists
  policy_rules: defineTable({
    workspaceId: v.id("workspaces"),
    ruleType: v.union(
      v.literal("block_user"),
      v.literal("block_keyword"),
      v.literal("allow_user")
    ),
    value: v.string(), // username, user_id, or keyword
    notes: v.optional(v.string()),
    created_at_ms: v.number(),
  }).index("by_workspace_type_value", ["workspaceId", "ruleType", "value"]),

  // Webhook config (for docs.twitterapi.io)
  webhook_rules: defineTable({
    workspaceId: v.id("workspaces"),
    rule_id: v.optional(v.string()),
    tag: v.string(),   // e.g. "mentions"
    value: v.string(), // query string to match @AskBuddy
    active: v.boolean(),
    interval_seconds: v.number(),
    created_at_ms: v.number(),
    updated_at_ms: v.number(),
  })
    .index("by_workspace_active", ["workspaceId", "active"])
    .index("by_workspace_tag_value", ["workspaceId", "tag", "value"]),
});
