type LogLevel = "info" | "error" | "warn" | "debug";

export function log(level: LogLevel, scope: string, fields: Record<string, any> = {}) {
  const base = {
    ts: new Date().toISOString(),
    scope,
    level,
  };
  try {
    const line = JSON.stringify({ ...base, ...fields });
    if (level === "error") console.error(line);
    else if (level === "warn") console.warn(line);
    else if (level === "debug") console.debug(line);
    else console.log(line);
  } catch (e) {
    console.log(`[log-fallback] ${level} ${scope}`, fields);
  }
}

export const logInfo = (scope: string, fields?: Record<string, any>) => log("info", scope, fields);
export const logError = (scope: string, fields?: Record<string, any>) => log("error", scope, fields);
export const logWarn = (scope: string, fields?: Record<string, any>) => log("warn", scope, fields);
export const logDebug = (scope: string, fields?: Record<string, any>) => log("debug", scope, fields);

