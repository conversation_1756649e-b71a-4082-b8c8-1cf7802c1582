import { logError, logInfo } from "./log";

export function verifyWebhookSecret(headers: Headers): boolean {
  const secret = process.env.TWITTER_WEBHOOK_SECRET;
  const header = headers.get("x-webhook-secret");
  const ok = !!secret && !!header && secret === header;
  if (!ok) logError("webhook.verify.fail", { hasHeader: !!header });
  return ok;
}

export type NormalizedMention = {
  tweetId: string;
  conversationId?: string;
  parentTweetId?: string;
  authorUserId: string;
  authorUsername?: string;
  text: string;
  urls: string[];
  lang?: string;
};

export function normalizeTwitterPayload(body: any): NormalizedMention | null {
  const evt = body?.data || body;
  const tweetId = evt?.tweet_id || evt?.id || evt?.tweet?.id;
  const authorUserId = evt?.user_id || evt?.author_id || evt?.user?.id;
  const text = evt?.text || evt?.tweet?.text || "";
  if (!tweetId || !authorUserId || !text) return null;

  const urls = Array.isArray(evt?.urls)
    ? evt.urls
    : (evt?.entities?.urls || []).map((u: any) => u.expanded_url || u.url).filter(Boolean);

  return {
    tweetId,
    conversationId: evt?.conversation_id || evt?.tweet?.conversation_id,
    parentTweetId: evt?.in_reply_to_status_id || evt?.tweet?.in_reply_to_status_id,
    authorUserId,
    authorUsername: evt?.username || evt?.user?.screen_name || evt?.user?.username,
    text,
    urls,
    lang: evt?.lang || evt?.tweet?.lang,
  };
}

