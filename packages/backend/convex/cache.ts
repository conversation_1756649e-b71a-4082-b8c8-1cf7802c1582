import { v } from "convex/values";
import { internalMutation } from "./_generated/server";
import type { Id } from "./_generated/dataModel";

// Cache Exa search results
export const cacheExaResults = internalMutation({
  args: {
    workspaceId: v.id("workspaces"),
    queryHash: v.string(),
    query: v.string(),
    data: v.any(),
    ttlMs: v.number(),
  },
  returns: v.null(),
  handler: async (ctx, { workspaceId, queryHash, query, data, ttlMs }) => {
    // Check if cache entry already exists
    const existing = await ctx.db
      .query("exa_cache")
      .withIndex("by_workspace_qhash", (q) => 
        q.eq("workspaceId", workspaceId).eq("query_hash", queryHash)
      )
      .first();

    if (existing) {
      // Update existing cache entry
      await ctx.db.patch(existing._id, {
        results: data,
        ttl_ms: ttlMs,
      });
    } else {
      // Create new cache entry
      await ctx.db.insert("exa_cache", {
        workspaceId,
        query_hash: queryHash,
        query,
        results: data,
        ttl_ms: ttlMs,
        created_at_ms: Date.now(),
      });
    }
  },
});

// Cache Firecrawl scraping results
export const cacheFirecrawlResults = internalMutation({
  args: {
    workspaceId: v.id("workspaces"),
    urlHash: v.string(),
    url: v.string(),
    formats: v.array(v.string()),
    data: v.any(),
    ttlMs: v.number(),
  },
  returns: v.null(),
  handler: async (ctx, { workspaceId, urlHash, url, formats, data, ttlMs }) => {
    // Check if cache entry already exists
    const existing = await ctx.db
      .query("firecrawl_cache")
      .withIndex("by_workspace_urlhash", (q) => 
        q.eq("workspaceId", workspaceId).eq("url_hash", urlHash)
      )
      .first();

    if (existing) {
      // Update existing cache entry
      await ctx.db.patch(existing._id, {
        data,
        ttl_ms: ttlMs,
      });
    } else {
      // Create new cache entry
      await ctx.db.insert("firecrawl_cache", {
        workspaceId,
        url_hash: urlHash,
        url,
        formats,
        data,
        ttl_ms: ttlMs,
        created_at_ms: Date.now(),
      });
    }
  },
});

// Cache Twitter API data
export const cacheTwitterData = internalMutation({
  args: {
    workspaceId: v.id("workspaces"),
    tweetId: v.string(),
    data: v.any(),
  },
  returns: v.null(),
  handler: async (ctx, { workspaceId, tweetId, data }) => {
    // Check if cache entry already exists
    const existing = await ctx.db
      .query("twitter_cache")
      .withIndex("by_workspace_tweet", (q) => 
        q.eq("workspaceId", workspaceId).eq("tweet_id", tweetId)
      )
      .first();

    if (existing) {
      // Update existing cache entry
      await ctx.db.patch(existing._id, {
        data,
        fetched_at_ms: Date.now(),
      });
    } else {
      // Create new cache entry
      await ctx.db.insert("twitter_cache", {
        workspaceId,
        tweet_id: tweetId,
        data,
        fetched_at_ms: Date.now(),
      });
    }
  },
});

// Clean expired Exa cache entries
export const cleanExpiredExaCache = internalMutation({
  args: { now: v.number() },
  returns: v.number(),
  handler: async (ctx, { now }) => {
    const expiredItems = await ctx.db
      .query("exa_cache")
      .filter((q) => q.lt(q.field("ttl_ms"), now))
      .collect();
    
    for (const item of expiredItems) {
      await ctx.db.delete(item._id);
    }
    
    return expiredItems.length;
  },
});

// Clean expired Firecrawl cache entries
export const cleanExpiredFirecrawlCache = internalMutation({
  args: { now: v.number() },
  returns: v.number(),
  handler: async (ctx, { now }) => {
    const expiredItems = await ctx.db
      .query("firecrawl_cache")
      .filter((q) => q.lt(q.field("ttl_ms"), now))
      .collect();
    
    for (const item of expiredItems) {
      await ctx.db.delete(item._id);
    }
    
    return expiredItems.length;
  },
});

// Clean expired Twitter cache entries (older than cutoffMs)
export const cleanExpiredTwitterCache = internalMutation({
  args: { cutoffMs: v.number() },
  returns: v.number(),
  handler: async (ctx, { cutoffMs }) => {
    const expiredItems = await ctx.db
      .query("twitter_cache")
      .filter((q) => q.lt(q.field("fetched_at_ms"), cutoffMs))
      .collect();
    
    for (const item of expiredItems) {
      await ctx.db.delete(item._id);
    }
    
    return expiredItems.length;
  },
});