name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: '1.2.19'

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Type check
        run: bun run check-types

      - name: Convex validate
        run: bunx convex dev --once
        env:
          # Provide dummy values for CI validation
          NEXT_PUBLIC_CONVEX_URL: http://localhost:3333
          OPENROUTER_API_KEY: test
          FIRECRAWL_API_KEY: test
          EXA_API_KEY: test
          XAI_API_KEY: test
          TWITTER_API_KEY: test

      - name: Lint (best-effort)
        run: |
          if bun run lint 2>/dev/null; then echo "lint ok"; else echo "lint not configured"; fi

